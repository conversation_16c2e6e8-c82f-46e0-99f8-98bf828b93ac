import React, { useState } from 'react'
import {
  Card,
  Rate,
  Button,
  Space,
  Typography,
  Input,
  Form,
  Radio,
  Progress,
  Statistic,
  Row,
  Col,
  Tag,
  Alert,
  message,
  Modal,
} from 'antd'
import {
  SmileOutlined,
  MehOutlined,
  FrownOutlined,
  TrophyOutlined,
  WarningOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons'
import { NPSResponse, NPSSurvey } from '../../types/feedback'

const { Title, Text, Paragraph } = Typography
const { TextArea } = Input

interface NPSRatingProps {
  survey?: NPSSurvey
  onSubmit: (response: Omit<NPSResponse, 'id'>) => void
  customerId?: string
  customerName?: string
  projectId?: string
  projectName?: string
  readonly?: boolean
  showAnalytics?: boolean
}

// Mock NPS data for analytics
const mockNPSData = {
  currentScore: 42,
  previousScore: 38,
  totalResponses: 156,
  promoters: 45,
  passives: 66,
  detractors: 45,
  responseRate: 78.5,
}

const NPSRating: React.FC<NPSRatingProps> = ({
  survey,
  onSubmit,
  customerId = 'customer_1',
  customerName = '张三',
  projectId,
  projectName,
  readonly = false,
  showAnalytics = false,
}) => {
  const [npsScore, setNpsScore] = useState<number | null>(null)
  const [feedback, setFeedback] = useState('')
  const [submitted, setSubmitted] = useState(false)
  const [form] = Form.useForm()

  const getNPSCategory = (score: number) => {
    if (score >= 9) return 'promoter'
    if (score >= 7) return 'passive'
    return 'detractor'
  }

  const getNPSCategoryText = (score: number) => {
    const category = getNPSCategory(score)
    const texts = {
      promoter: '推荐者',
      passive: '中立者',
      detractor: '批评者',
    }
    return texts[category]
  }

  const getNPSCategoryColor = (score: number) => {
    const category = getNPSCategory(score)
    const colors = {
      promoter: '#52c41a',
      passive: '#faad14',
      detractor: '#ff4d4f',
    }
    return colors[category]
  }

  const getNPSCategoryIcon = (score: number) => {
    const category = getNPSCategory(score)
    const icons = {
      promoter: <SmileOutlined />,
      passive: <MehOutlined />,
      detractor: <FrownOutlined />,
    }
    return icons[category]
  }

  const calculateNPSScore = (promoters: number, detractors: number, total: number) => {
    if (total === 0) return 0
    const promoterRate = (promoters / total) * 100
    const detractorRate = (detractors / total) * 100
    return Math.round(promoterRate - detractorRate)
  }

  const handleSubmit = async () => {
    if (npsScore === null) {
      message.error('请先进行评分')
      return
    }

    try {
      const response = {
        surveyId: survey?.id || 'default_survey',
        customerId,
        customerName,
        customerEmail: `${customerName}@example.com`,
        projectId,
        projectName,
        npsScore,
        answers: [
          {
            id: 'answer_1',
            responseId: '',
            questionId: 'nps_question',
            questionType: 'nps',
            value: npsScore,
          },
          {
            id: 'answer_2',
            responseId: '',
            questionId: 'feedback_question',
            questionType: 'text',
            value: feedback,
            text: feedback,
          },
        ],
        completedAt: new Date().toISOString(),
        source: 'web',
        metadata: {
          projectId,
          projectName,
        },
      }

      await onSubmit(response)
      setSubmitted(true)
      message.success('感谢您的反馈！')
    } catch (error) {
      message.error('提交失败，请重试')
    }
  }

  const renderNPSScale = () => (
    <div style={{ textAlign: 'center', margin: '24px 0' }}>
      <Title level={4}>您向朋友或同事推荐我们服务的可能性有多大？</Title>
      <Text type="secondary">请在0-10分中选择一个分数（0=完全不可能，10=非常可能）</Text>
      
      <div style={{ margin: '24px 0' }}>
        <Radio.Group
          value={npsScore}
          onChange={(e) => setNpsScore(e.target.value)}
          disabled={readonly || submitted}
        >
          <Row gutter={[8, 8]} justify="center">
            {Array.from({ length: 11 }, (_, i) => (
              <Col key={i}>
                <Radio.Button
                  value={i}
                  style={{
                    width: 40,
                    height: 40,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: 16,
                    fontWeight: 'bold',
                  }}
                >
                  {i}
                </Radio.Button>
              </Col>
            ))}
          </Row>
        </Radio.Group>
      </div>

      <Row justify="space-between" style={{ marginTop: 8 }}>
        <Col>
          <Text type="secondary" style={{ fontSize: 12 }}>
            完全不可能
          </Text>
        </Col>
        <Col>
          <Text type="secondary" style={{ fontSize: 12 }}>
            非常可能
          </Text>
        </Col>
      </Row>

      {npsScore !== null && (
        <div style={{ marginTop: 16 }}>
          <Space>
            {getNPSCategoryIcon(npsScore)}
            <Text strong style={{ color: getNPSCategoryColor(npsScore) }}>
              {getNPSCategoryText(npsScore)}
            </Text>
          </Space>
        </div>
      )}
    </div>
  )

  const renderFeedbackForm = () => (
    <div style={{ marginTop: 24 }}>
      <Title level={5}>请告诉我们您的想法</Title>
      <TextArea
        value={feedback}
        onChange={(e) => setFeedback(e.target.value)}
        placeholder={
          npsScore !== null && npsScore >= 9
            ? '您最喜欢我们服务的哪些方面？'
            : npsScore !== null && npsScore <= 6
            ? '我们如何改进我们的服务？'
            : '请分享您的想法和建议...'
        }
        rows={4}
        disabled={readonly || submitted}
      />
    </div>
  )

  const renderAnalytics = () => {
    if (!showAnalytics) return null

    const npsScore = calculateNPSScore(
      mockNPSData.promoters,
      mockNPSData.detractors,
      mockNPSData.totalResponses
    )

    const promoterRate = (mockNPSData.promoters / mockNPSData.totalResponses) * 100
    const passiveRate = (mockNPSData.passives / mockNPSData.totalResponses) * 100
    const detractorRate = (mockNPSData.detractors / mockNPSData.totalResponses) * 100

    return (
      <Card title="NPS 分析" style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="NPS 得分"
                value={npsScore}
                prefix={<TrophyOutlined />}
                valueStyle={{
                  color: npsScore >= 50 ? '#52c41a' : npsScore >= 0 ? '#faad14' : '#ff4d4f'
                }}
              />
              <div style={{ marginTop: 8 }}>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  {npsScore > mockNPSData.previousScore ? '↗' : '↘'} 
                  {Math.abs(npsScore - mockNPSData.previousScore)} 
                  (vs 上期)
                </Text>
              </div>
            </Card>
          </Col>
          
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="总回复数"
                value={mockNPSData.totalResponses}
                suffix="份"
              />
              <div style={{ marginTop: 8 }}>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  回复率: {mockNPSData.responseRate}%
                </Text>
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12}>
            <Card title="分布情况">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                    <Space>
                      <SmileOutlined style={{ color: '#52c41a' }} />
                      <Text>推荐者 (9-10分)</Text>
                    </Space>
                    <Text strong>{mockNPSData.promoters} ({promoterRate.toFixed(1)}%)</Text>
                  </div>
                  <Progress percent={promoterRate} strokeColor="#52c41a" showInfo={false} />
                </div>

                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                    <Space>
                      <MehOutlined style={{ color: '#faad14' }} />
                      <Text>中立者 (7-8分)</Text>
                    </Space>
                    <Text strong>{mockNPSData.passives} ({passiveRate.toFixed(1)}%)</Text>
                  </div>
                  <Progress percent={passiveRate} strokeColor="#faad14" showInfo={false} />
                </div>

                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                    <Space>
                      <FrownOutlined style={{ color: '#ff4d4f' }} />
                      <Text>批评者 (0-6分)</Text>
                    </Space>
                    <Text strong>{mockNPSData.detractors} ({detractorRate.toFixed(1)}%)</Text>
                  </div>
                  <Progress percent={detractorRate} strokeColor="#ff4d4f" showInfo={false} />
                </div>
              </Space>
            </Card>
          </Col>
        </Row>

        {npsScore < 0 && (
          <Alert
            message="NPS得分偏低"
            description="当前NPS得分为负数，建议重点关注客户反馈，改进服务质量"
            type="warning"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}

        {npsScore >= 50 && (
          <Alert
            message="优秀的NPS得分"
            description="恭喜！您的NPS得分表现优秀，客户满意度很高"
            type="success"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </Card>
    )
  }

  const renderThankYou = () => (
    <div style={{ textAlign: 'center', padding: '40px 20px' }}>
      <CheckCircleOutlined style={{ fontSize: 64, color: '#52c41a', marginBottom: 16 }} />
      <Title level={3}>感谢您的反馈！</Title>
      <Paragraph>
        您的意见对我们非常重要，我们会认真对待每一条反馈，
        持续改进我们的服务质量。
      </Paragraph>
      {npsScore !== null && npsScore <= 6 && (
        <Alert
          message="我们会跟进"
          description="我们的客户成功团队会在24小时内与您联系，了解详细情况并协助解决问题。"
          type="info"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}
    </div>
  )

  if (submitted) {
    return (
      <Card>
        {renderThankYou()}
        {showAnalytics && renderAnalytics()}
      </Card>
    )
  }

  return (
    <div>
      <Card>
        {projectName && (
          <div style={{ marginBottom: 16 }}>
            <Tag color="blue">{projectName}</Tag>
            <Text type="secondary">项目满意度调研</Text>
          </div>
        )}

        {renderNPSScale()}
        {renderFeedbackForm()}

        {!readonly && (
          <div style={{ textAlign: 'center', marginTop: 24 }}>
            <Button
              type="primary"
              size="large"
              onClick={handleSubmit}
              disabled={npsScore === null}
            >
              提交反馈
            </Button>
          </div>
        )}
      </Card>

      {showAnalytics && renderAnalytics()}
    </div>
  )
}

export default NPSRating
