import React, { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Tag,
  Select,
  DatePicker,
  Space,
  Typography,
  Alert,
  List,
  Avatar,
  Tooltip,
  Button,
} from 'antd'
import {
  SmileOutlined,
  FrownOutlined,
  TrophyOutlined,
  MessageOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined,
  Exclamation<PERSON>ircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons'
import { SatisfactionMetrics, FeedbackDashboard, CustomerFeedback } from '../../types/feedback'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

interface SatisfactionDashboardProps {
  data: FeedbackDashboard
  onPeriodChange: (period: string) => void
  onDateRangeChange: (dates: any) => void
  onFeedbackClick: (feedbackId: string) => void
}

// Mock dashboard data
const mockDashboardData: FeedbackDashboard = {
  metrics: {
    overallSatisfaction: 4.2,
    npsScore: 42,
    responseRate: 78.5,
    totalFeedbacks: 156,
    resolvedFeedbacks: 142,
    averageResolutionTime: 2.3,
    customerRetentionRate: 89.2,
    repeatCustomerRate: 67.8,
    categoryBreakdown: [
      { category: '产品功能', count: 45, averageRating: 4.1, satisfactionRate: 82, resolutionRate: 95 },
      { category: '服务质量', count: 38, averageRating: 4.5, satisfactionRate: 89, resolutionRate: 100 },
      { category: '响应速度', count: 32, averageRating: 3.8, satisfactionRate: 75, resolutionRate: 88 },
      { category: '技术支持', count: 28, averageRating: 4.3, satisfactionRate: 86, resolutionRate: 93 },
      { category: '项目管理', count: 13, averageRating: 4.0, satisfactionRate: 80, resolutionRate: 92 },
    ],
    trendData: [
      { date: '2024-01-01', satisfaction: 4.0, nps: 35, feedbacks: 12, resolved: 11 },
      { date: '2024-01-08', satisfaction: 4.1, nps: 38, feedbacks: 15, resolved: 14 },
      { date: '2024-01-15', satisfaction: 4.2, nps: 42, feedbacks: 18, resolved: 17 },
      { date: '2024-01-22', satisfaction: 4.3, nps: 45, feedbacks: 16, resolved: 15 },
    ],
    topIssues: [
      { issue: '登录响应慢', count: 8, severity: 'medium', averageResolutionTime: 1.5, resolutionRate: 100 },
      { issue: '功能不够直观', count: 6, severity: 'low', averageResolutionTime: 3.2, resolutionRate: 83 },
      { issue: '文档不够详细', count: 5, severity: 'low', averageResolutionTime: 2.8, resolutionRate: 100 },
    ],
    topCompliments: [
      { aspect: '客服态度', count: 15, averageRating: 4.8, examples: ['服务很专业', '响应很及时'] },
      { aspect: '产品稳定性', count: 12, averageRating: 4.6, examples: ['系统很稳定', '很少出问题'] },
      { aspect: '功能实用', count: 10, averageRating: 4.5, examples: ['功能很实用', '解决了痛点'] },
    ],
  },
  recentFeedbacks: [
    {
      id: 'feedback_1',
      customerName: '张三',
      type: 'satisfaction',
      title: '整体服务很满意',
      rating: 5,
      status: 'resolved',
      createdAt: '2024-01-22T10:30:00Z',
    } as CustomerFeedback,
    {
      id: 'feedback_2',
      customerName: '李四',
      type: 'bug_report',
      title: '登录页面加载慢',
      priority: 'medium',
      status: 'in_progress',
      createdAt: '2024-01-22T09:15:00Z',
    } as CustomerFeedback,
  ],
  pendingActions: [
    {
      id: 'action_1',
      feedbackId: 'feedback_2',
      type: 'response_required',
      title: '需要回复客户反馈',
      description: '李四反馈的登录问题需要技术团队回复',
      priority: 'medium',
      assignedTo: '技术支持',
      dueDate: '2024-01-23T18:00:00Z',
      createdAt: '2024-01-22T09:15:00Z',
    },
  ],
  activeSurveys: [],
  alerts: [
    {
      id: 'alert_1',
      type: 'trend_decline',
      title: 'NPS得分下降',
      message: '本周NPS得分相比上周下降了3分，需要关注',
      severity: 'warning',
      data: { current: 42, previous: 45 },
      createdAt: '2024-01-22T08:00:00Z',
    },
  ],
  recommendations: [
    {
      id: 'rec_1',
      type: 'process_improvement',
      title: '优化响应速度',
      description: '建议优化客服响应流程，提高首次响应速度',
      impact: 'high',
      effort: 'medium',
      priority: 1,
      actions: ['培训客服团队', '优化工单系统', '增加自动回复'],
    },
  ],
}

const SatisfactionDashboard: React.FC<SatisfactionDashboardProps> = ({
  data = mockDashboardData,
  onPeriodChange,
  onDateRangeChange,
  onFeedbackClick,
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState('month')

  const getSatisfactionColor = (score: number) => {
    if (score >= 4.5) return '#52c41a'
    if (score >= 4.0) return '#1890ff'
    if (score >= 3.5) return '#faad14'
    return '#ff4d4f'
  }

  const getNPSColor = (score: number) => {
    if (score >= 50) return '#52c41a'
    if (score >= 0) return '#faad14'
    return '#ff4d4f'
  }

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'orange',
      reviewed: 'blue',
      in_progress: 'processing',
      resolved: 'success',
      closed: 'default',
      rejected: 'error',
    }
    return colors[status as keyof typeof colors]
  }

  const getStatusText = (status: string) => {
    const texts = {
      pending: '待处理',
      reviewed: '已查看',
      in_progress: '处理中',
      resolved: '已解决',
      closed: '已关闭',
      rejected: '已拒绝',
    }
    return texts[status as keyof typeof texts]
  }

  const categoryColumns: ColumnsType<any> = [
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '反馈数量',
      dataIndex: 'count',
      key: 'count',
      render: (count) => <Text strong>{count}</Text>,
    },
    {
      title: '平均评分',
      dataIndex: 'averageRating',
      key: 'averageRating',
      render: (rating) => (
        <Space>
          <Text>{rating.toFixed(1)}</Text>
          <Progress
            percent={(rating / 5) * 100}
            size="small"
            strokeColor={getSatisfactionColor(rating)}
            showInfo={false}
            style={{ width: 60 }}
          />
        </Space>
      ),
    },
    {
      title: '满意率',
      dataIndex: 'satisfactionRate',
      key: 'satisfactionRate',
      render: (rate) => (
        <Text style={{ color: rate >= 80 ? '#52c41a' : rate >= 60 ? '#faad14' : '#ff4d4f' }}>
          {rate}%
        </Text>
      ),
    },
    {
      title: '解决率',
      dataIndex: 'resolutionRate',
      key: 'resolutionRate',
      render: (rate) => (
        <Text style={{ color: rate >= 90 ? '#52c41a' : rate >= 70 ? '#faad14' : '#ff4d4f' }}>
          {rate}%
        </Text>
      ),
    },
  ]

  const feedbackColumns: ColumnsType<CustomerFeedback> = [
    {
      title: '客户',
      dataIndex: 'customerName',
      key: 'customerName',
      render: (name) => (
        <Space>
          <Avatar size="small">{name[0]}</Avatar>
          <Text>{name}</Text>
        </Space>
      ),
    },
    {
      title: '反馈内容',
      dataIndex: 'title',
      key: 'title',
      render: (title, record) => (
        <div>
          <Text strong>{title}</Text>
          {record.rating && (
            <div style={{ marginTop: 4 }}>
              <Space>
                {Array.from({ length: record.rating }, (_, i) => (
                  <SmileOutlined key={i} style={{ color: '#faad14' }} />
                ))}
              </Space>
            </div>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString('zh-CN'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Button
          type="link"
          size="small"
          onClick={() => onFeedbackClick(record.id)}
        >
          查看
        </Button>
      ),
    },
  ]

  return (
    <div>
      {/* Controls */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={4} style={{ margin: 0 }}>客户满意度分析</Title>
          </Col>
          <Col>
            <Space>
              <Select
                value={selectedPeriod}
                onChange={(value) => {
                  setSelectedPeriod(value)
                  onPeriodChange(value)
                }}
                style={{ width: 120 }}
              >
                <Option value="week">本周</Option>
                <Option value="month">本月</Option>
                <Option value="quarter">本季度</Option>
                <Option value="year">本年</Option>
              </Select>
              <RangePicker onChange={onDateRangeChange} />
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Alerts */}
      {data.alerts.length > 0 && (
        <div style={{ marginBottom: 16 }}>
          {data.alerts.map(alert => (
            <Alert
              key={alert.id}
              message={alert.title}
              description={alert.message}
              type={alert.severity}
              showIcon
              style={{ marginBottom: 8 }}
            />
          ))}
        </div>
      )}

      {/* Key Metrics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="整体满意度"
              value={data.metrics.overallSatisfaction}
              precision={1}
              suffix="/5.0"
              prefix={<SmileOutlined />}
              valueStyle={{ color: getSatisfactionColor(data.metrics.overallSatisfaction) }}
            />
            <div style={{ marginTop: 8 }}>
              <Progress
                percent={(data.metrics.overallSatisfaction / 5) * 100}
                strokeColor={getSatisfactionColor(data.metrics.overallSatisfaction)}
                showInfo={false}
              />
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="NPS得分"
              value={data.metrics.npsScore}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: getNPSColor(data.metrics.npsScore) }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                {data.metrics.npsScore > 0 ? '↗' : '↘'} 
                {Math.abs(data.metrics.npsScore - 38)} (vs 上期)
              </Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="反馈总数"
              value={data.metrics.totalFeedbacks}
              suffix="条"
              prefix={<MessageOutlined />}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                回复率: {data.metrics.responseRate}%
              </Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="解决率"
              value={Math.round((data.metrics.resolvedFeedbacks / data.metrics.totalFeedbacks) * 100)}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                平均 {data.metrics.averageResolutionTime} 天
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Secondary Metrics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12}>
          <Card>
            <Statistic
              title="客户留存率"
              value={data.metrics.customerRetentionRate}
              suffix="%"
              prefix={<TrendingUpOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12}>
          <Card>
            <Statistic
              title="复购率"
              value={data.metrics.repeatCustomerRate}
              suffix="%"
              prefix={<TrendingUpOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Category Analysis */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={14}>
          <Card title="分类分析">
            <Table
              columns={categoryColumns}
              dataSource={data.metrics.categoryBreakdown}
              rowKey="category"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col xs={24} lg={10}>
          <Card title="待处理事项">
            <List
              dataSource={data.pendingActions}
              renderItem={(action) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        icon={<ClockCircleOutlined />}
                        style={{
                          backgroundColor: action.priority === 'high' ? '#ff4d4f' : 
                                          action.priority === 'medium' ? '#faad14' : '#52c41a'
                        }}
                      />
                    }
                    title={action.title}
                    description={
                      <div>
                        <Text type="secondary">{action.description}</Text>
                        <br />
                        <Space style={{ marginTop: 4 }}>
                          <Text style={{ fontSize: 12 }}>负责人: {action.assignedTo}</Text>
                          <Text style={{ fontSize: 12 }}>
                            截止: {new Date(action.dueDate).toLocaleDateString('zh-CN')}
                          </Text>
                        </Space>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* Recent Feedbacks and Top Issues */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="最近反馈">
            <Table
              columns={feedbackColumns}
              dataSource={data.recentFeedbacks}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="主要问题">
            <List
              dataSource={data.metrics.topIssues}
              renderItem={(issue) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        icon={<ExclamationCircleOutlined />}
                        style={{
                          backgroundColor: issue.severity === 'high' ? '#ff4d4f' : 
                                          issue.severity === 'medium' ? '#faad14' : '#52c41a'
                        }}
                      />
                    }
                    title={issue.issue}
                    description={
                      <Space>
                        <Text style={{ fontSize: 12 }}>出现 {issue.count} 次</Text>
                        <Text style={{ fontSize: 12 }}>平均 {issue.averageResolutionTime} 天解决</Text>
                        <Text style={{ fontSize: 12 }}>解决率 {issue.resolutionRate}%</Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* Recommendations */}
      <Card title="改进建议">
        <List
          dataSource={data.recommendations}
          renderItem={(rec) => (
            <List.Item
              actions={[
                <Button type="link" size="small">查看详情</Button>
              ]}
            >
              <List.Item.Meta
                avatar={
                  <Avatar
                    style={{
                      backgroundColor: rec.impact === 'high' ? '#52c41a' : 
                                      rec.impact === 'medium' ? '#1890ff' : '#faad14'
                    }}
                  >
                    {rec.priority}
                  </Avatar>
                }
                title={rec.title}
                description={
                  <div>
                    <Text>{rec.description}</Text>
                    <div style={{ marginTop: 8 }}>
                      <Space>
                        <Tag color="blue">影响: {rec.impact}</Tag>
                        <Tag color="orange">工作量: {rec.effort}</Tag>
                      </Space>
                    </div>
                    <div style={{ marginTop: 8 }}>
                      <Text strong style={{ fontSize: 12 }}>建议行动:</Text>
                      <ul style={{ margin: '4px 0 0 16px', fontSize: 12 }}>
                        {rec.actions.map((action, index) => (
                          <li key={index}>{action}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Card>
    </div>
  )
}

export default SatisfactionDashboard
