import React, { useState } from 'react'
import {
  Card,
  Form,
  Input,
  Select,
  Rate,
  Button,
  Upload,
  Space,
  Typography,
  Tag,
  Alert,
  Row,
  Col,
  Radio,
  Checkbox,
  message,
  Progress,
} from 'antd'
import {
  UploadOutlined,
  StarOutlined,
  BugOutlined,
  BulbOutlined,
  MessageOutlined,
  SmileOutlined,
  FrownOutlined,
} from '@ant-design/icons'
import { CustomerFeedback, FeedbackType } from '../../types/feedback'
import type { UploadFile } from 'antd/es/upload/interface'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

interface FeedbackFormProps {
  customerId?: string
  customerName?: string
  projectId?: string
  projectName?: string
  initialType?: FeedbackType
  onSubmit: (feedback: Omit<CustomerFeedback, 'id'>) => void
  onCancel?: () => void
  readonly?: boolean
}

const FeedbackForm: React.FC<FeedbackFormProps> = ({
  customerId = 'customer_1',
  customerName = '张三',
  projectId,
  projectName,
  initialType = FeedbackType.GENERAL,
  onSubmit,
  onCancel,
  readonly = false,
}) => {
  const [form] = Form.useForm()
  const [feedbackType, setFeedbackType] = useState<FeedbackType>(initialType)
  const [uploading, setUploading] = useState(false)
  const [fileList, setFileList] = useState<UploadFile[]>([])
  const [currentStep, setCurrentStep] = useState(0)

  const feedbackTypes = [
    {
      type: FeedbackType.SATISFACTION,
      label: '满意度反馈',
      icon: <SmileOutlined />,
      color: 'green',
      description: '对我们服务的整体满意度评价',
    },
    {
      type: FeedbackType.FEATURE_REQUEST,
      label: '功能建议',
      icon: <BulbOutlined />,
      color: 'blue',
      description: '希望我们增加的新功能或改进',
    },
    {
      type: FeedbackType.BUG_REPORT,
      label: '问题报告',
      icon: <BugOutlined />,
      color: 'red',
      description: '发现的系统问题或错误',
    },
    {
      type: FeedbackType.COMPLAINT,
      label: '投诉建议',
      icon: <FrownOutlined />,
      color: 'orange',
      description: '对服务不满意的地方',
    },
    {
      type: FeedbackType.COMPLIMENT,
      label: '表扬反馈',
      icon: <StarOutlined />,
      color: 'gold',
      description: '对我们服务的表扬和认可',
    },
    {
      type: FeedbackType.GENERAL,
      label: '一般反馈',
      icon: <MessageOutlined />,
      color: 'default',
      description: '其他意见和建议',
    },
  ]

  const categories = [
    '产品功能',
    '服务质量',
    '响应速度',
    '技术支持',
    '项目管理',
    '沟通协作',
    '文档资料',
    '培训服务',
    '价格费用',
    '其他',
  ]

  const handleSubmit = async (values: any) => {
    try {
      const feedback: Omit<CustomerFeedback, 'id'> = {
        customerId,
        customerName,
        customerEmail: `${customerName}@example.com`,
        projectId,
        projectName,
        type: feedbackType,
        status: 'pending',
        title: values.title,
        content: values.content,
        rating: values.rating,
        category: values.category,
        priority: values.priority || 'medium',
        tags: values.tags || [],
        attachments: fileList.map(file => ({
          id: file.uid,
          feedbackId: '',
          name: file.name,
          url: file.url || '',
          size: file.size || 0,
          type: file.type || '',
          uploadedAt: new Date().toISOString(),
        })),
        responses: [],
        source: 'web',
        metadata: {
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
          formVersion: '1.0',
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      await onSubmit(feedback)
      message.success('反馈提交成功，我们会尽快处理！')
      form.resetFields()
      setFileList([])
      setCurrentStep(0)
    } catch (error) {
      message.error('提交失败，请重试')
    }
  }

  const handleUpload = (file: File) => {
    setUploading(true)
    // Simulate upload
    setTimeout(() => {
      setUploading(false)
      message.success('文件上传成功')
    }, 1000)
    return false
  }

  const renderTypeSelection = () => (
    <div>
      <Title level={4}>请选择反馈类型</Title>
      <Row gutter={[16, 16]}>
        {feedbackTypes.map((type) => (
          <Col xs={24} sm={12} md={8} key={type.type}>
            <Card
              hoverable
              className={feedbackType === type.type ? 'selected' : ''}
              onClick={() => setFeedbackType(type.type)}
              style={{
                border: feedbackType === type.type ? '2px solid #1890ff' : '1px solid #d9d9d9',
                cursor: 'pointer',
              }}
            >
              <Space direction="vertical" style={{ textAlign: 'center', width: '100%' }}>
                <div style={{ fontSize: 24, color: type.color === 'default' ? '#666' : type.color }}>
                  {type.icon}
                </div>
                <Text strong>{type.label}</Text>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  {type.description}
                </Text>
              </Space>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  )

  const renderFeedbackForm = () => (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      disabled={readonly}
    >
      <Row gutter={16}>
        <Col xs={24} md={16}>
          <Form.Item
            name="title"
            label="反馈标题"
            rules={[{ required: true, message: '请输入反馈标题' }]}
          >
            <Input placeholder="请简要描述您的反馈内容" />
          </Form.Item>
        </Col>
        <Col xs={24} md={8}>
          <Form.Item
            name="category"
            label="反馈分类"
            rules={[{ required: true, message: '请选择反馈分类' }]}
          >
            <Select placeholder="请选择分类">
              {categories.map(category => (
                <Option key={category} value={category}>
                  {category}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      {(feedbackType === FeedbackType.SATISFACTION || feedbackType === FeedbackType.COMPLIMENT) && (
        <Form.Item
          name="rating"
          label="满意度评分"
          rules={[{ required: true, message: '请进行评分' }]}
        >
          <Rate allowHalf />
        </Form.Item>
      )}

      <Form.Item
        name="content"
        label="详细描述"
        rules={[{ required: true, message: '请输入详细描述' }]}
      >
        <TextArea
          rows={6}
          placeholder={
            feedbackType === FeedbackType.BUG_REPORT
              ? '请详细描述遇到的问题，包括操作步骤、预期结果和实际结果...'
              : feedbackType === FeedbackType.FEATURE_REQUEST
              ? '请描述您希望增加的功能，以及这个功能如何帮助您...'
              : '请详细描述您的反馈内容...'
          }
        />
      </Form.Item>

      {feedbackType === FeedbackType.BUG_REPORT && (
        <Form.Item name="priority" label="问题严重程度">
          <Radio.Group>
            <Radio value="low">轻微 - 不影响正常使用</Radio>
            <Radio value="medium">一般 - 影响部分功能</Radio>
            <Radio value="high">严重 - 影响主要功能</Radio>
            <Radio value="critical">紧急 - 系统无法使用</Radio>
          </Radio.Group>
        </Form.Item>
      )}

      <Form.Item name="tags" label="标签">
        <Select
          mode="tags"
          placeholder="添加相关标签（可选）"
          style={{ width: '100%' }}
        >
          <Option value="UI/UX">UI/UX</Option>
          <Option value="性能">性能</Option>
          <Option value="安全">安全</Option>
          <Option value="易用性">易用性</Option>
          <Option value="兼容性">兼容性</Option>
        </Select>
      </Form.Item>

      <Form.Item label="附件">
        <Upload
          fileList={fileList}
          onChange={({ fileList }) => setFileList(fileList)}
          beforeUpload={handleUpload}
          multiple
        >
          <Button icon={<UploadOutlined />} loading={uploading}>
            上传文件
          </Button>
        </Upload>
        <Text type="secondary" style={{ fontSize: 12, marginTop: 8, display: 'block' }}>
          支持图片、文档等格式，单个文件不超过10MB
        </Text>
      </Form.Item>

      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit" size="large">
            提交反馈
          </Button>
          {onCancel && (
            <Button onClick={onCancel} size="large">
              取消
            </Button>
          )}
        </Space>
      </Form.Item>
    </Form>
  )

  const steps = [
    {
      title: '选择类型',
      content: renderTypeSelection(),
    },
    {
      title: '填写反馈',
      content: renderFeedbackForm(),
    },
  ]

  return (
    <div>
      {projectName && (
        <Alert
          message={`项目反馈: ${projectName}`}
          type="info"
          style={{ marginBottom: 16 }}
        />
      )}

      <Card>
        <div style={{ marginBottom: 24 }}>
          <Progress
            percent={((currentStep + 1) / steps.length) * 100}
            showInfo={false}
            strokeColor="#1890ff"
          />
          <div style={{ marginTop: 8, textAlign: 'center' }}>
            <Text type="secondary">
              步骤 {currentStep + 1} / {steps.length}: {steps[currentStep].title}
            </Text>
          </div>
        </div>

        {currentStep === 0 && (
          <div>
            {renderTypeSelection()}
            <div style={{ textAlign: 'center', marginTop: 24 }}>
              <Button
                type="primary"
                onClick={() => setCurrentStep(1)}
                disabled={!feedbackType}
              >
                下一步
              </Button>
            </div>
          </div>
        )}

        {currentStep === 1 && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Text strong>反馈类型:</Text>
                <Tag color={feedbackTypes.find(t => t.type === feedbackType)?.color}>
                  {feedbackTypes.find(t => t.type === feedbackType)?.label}
                </Tag>
                <Button
                  type="link"
                  size="small"
                  onClick={() => setCurrentStep(0)}
                >
                  修改
                </Button>
              </Space>
            </div>
            {renderFeedbackForm()}
          </div>
        )}
      </Card>

      <Card style={{ marginTop: 16 }}>
        <Title level={5}>反馈处理流程</Title>
        <Row gutter={16}>
          <Col xs={24} sm={8}>
            <div style={{ textAlign: 'center', padding: 16 }}>
              <div style={{ fontSize: 24, color: '#1890ff', marginBottom: 8 }}>1</div>
              <Text strong>提交反馈</Text>
              <br />
              <Text type="secondary" style={{ fontSize: 12 }}>
                填写并提交您的反馈
              </Text>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <div style={{ textAlign: 'center', padding: 16 }}>
              <div style={{ fontSize: 24, color: '#52c41a', marginBottom: 8 }}>2</div>
              <Text strong>团队处理</Text>
              <br />
              <Text type="secondary" style={{ fontSize: 12 }}>
                我们会在24小时内响应
              </Text>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <div style={{ textAlign: 'center', padding: 16 }}>
              <div style={{ fontSize: 24, color: '#faad14', marginBottom: 8 }}>3</div>
              <Text strong>跟进反馈</Text>
              <br />
              <Text type="secondary" style={{ fontSize: 12 }}>
                持续跟进直到问题解决
              </Text>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  )
}

export default FeedbackForm
