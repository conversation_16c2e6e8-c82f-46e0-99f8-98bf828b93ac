import React, { useState } from 'react'
import {
  Card,
  Steps,
  Button,
  Space,
  Typography,
  Tag,
  Modal,
  Form,
  Input,
  Rate,
  Select,
  Timeline,
  Alert,
  Progress,
  List,
  Avatar,
  Tooltip,
  Badge,
  message,
} from 'antd'
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  EditOutlined,
  MessageOutlined,
  WarningOutlined,
} from '@ant-design/icons'
import { AcceptanceWorkflow, AcceptanceStep, AcceptanceStatus } from '../../types/delivery'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select
const { Step } = Steps

interface AcceptanceWorkflowProps {
  packageId: string
  workflow: AcceptanceWorkflow
  onStepAction: (stepId: string, action: string, data?: any) => void
  onWorkflowUpdate: (updates: Partial<AcceptanceWorkflow>) => void
  currentUserId: string
  readonly?: boolean
}

// Mock workflow data
const mockWorkflow: AcceptanceWorkflow = {
  id: 'workflow_1',
  packageId: 'package_1',
  name: '标准验收流程',
  description: '包含文档审查、功能测试、性能测试和最终验收的完整流程',
  steps: [
    {
      id: 'step_1',
      workflowId: 'workflow_1',
      name: '文档审查',
      description: '审查交付文档的完整性和准确性',
      type: 'review',
      order: 1,
      status: 'completed',
      assigneeId: 'user_1',
      assigneeName: '张审查员',
      dueDate: '2024-01-25T18:00:00Z',
      completedAt: '2024-01-24T16:30:00Z',
      requirements: ['用户手册', '技术文档', 'API文档'],
      deliverables: ['文档审查报告'],
      actions: [
        {
          id: 'action_1',
          stepId: 'step_1',
          type: 'approve',
          name: '通过审查',
          description: '文档审查通过，可以进入下一步',
          parameters: {},
          isAvailable: false,
        },
        {
          id: 'action_2',
          stepId: 'step_1',
          type: 'reject',
          name: '拒绝',
          description: '文档不符合要求，需要修改',
          parameters: {},
          isAvailable: false,
        },
      ],
      conditions: [
        {
          id: 'condition_1',
          stepId: 'step_1',
          type: 'custom',
          description: '所有必需文档已提交',
          parameters: {},
          isMet: true,
        },
      ],
    },
    {
      id: 'step_2',
      workflowId: 'workflow_1',
      name: '功能测试',
      description: '执行功能测试用例，验证系统功能',
      type: 'test',
      order: 2,
      status: 'in_progress',
      assigneeId: 'user_2',
      assigneeName: '李测试员',
      dueDate: '2024-01-30T18:00:00Z',
      requirements: ['测试环境', '测试数据', '测试用例'],
      deliverables: ['功能测试报告'],
      actions: [
        {
          id: 'action_3',
          stepId: 'step_2',
          type: 'approve',
          name: '测试通过',
          description: '功能测试全部通过',
          parameters: {},
          isAvailable: true,
        },
        {
          id: 'action_4',
          stepId: 'step_2',
          type: 'reject',
          name: '测试失败',
          description: '存在功能缺陷，需要修复',
          parameters: {},
          isAvailable: true,
        },
      ],
      conditions: [
        {
          id: 'condition_2',
          stepId: 'step_2',
          type: 'all_tests_passed',
          description: '所有测试用例通过',
          parameters: {},
          isMet: false,
        },
      ],
    },
    {
      id: 'step_3',
      workflowId: 'workflow_1',
      name: '性能测试',
      description: '验证系统性能指标',
      type: 'test',
      order: 3,
      status: 'pending',
      assigneeId: 'user_3',
      assigneeName: '王性能工程师',
      dueDate: '2024-02-05T18:00:00Z',
      requirements: ['性能测试环境', '负载测试工具'],
      deliverables: ['性能测试报告'],
      actions: [
        {
          id: 'action_5',
          stepId: 'step_3',
          type: 'approve',
          name: '性能达标',
          description: '性能指标满足要求',
          parameters: {},
          isAvailable: false,
        },
      ],
      conditions: [
        {
          id: 'condition_3',
          stepId: 'step_3',
          type: 'quality_score_above',
          description: '性能评分达到80分以上',
          parameters: { threshold: 80 },
          isMet: false,
        },
      ],
    },
    {
      id: 'step_4',
      workflowId: 'workflow_1',
      name: '最终验收',
      description: '客户最终验收确认',
      type: 'approval',
      order: 4,
      status: 'pending',
      assigneeId: 'user_4',
      assigneeName: '客户代表',
      dueDate: '2024-02-10T18:00:00Z',
      requirements: ['所有测试通过', '文档完整'],
      deliverables: ['验收确认书'],
      actions: [
        {
          id: 'action_6',
          stepId: 'step_4',
          type: 'approve',
          name: '验收通过',
          description: '项目验收通过',
          parameters: {},
          isAvailable: false,
        },
        {
          id: 'action_7',
          stepId: 'step_4',
          type: 'reject',
          name: '验收不通过',
          description: '需要进一步修改',
          parameters: {},
          isAvailable: false,
        },
      ],
      conditions: [],
    },
  ],
  currentStepId: 'step_2',
  status: AcceptanceStatus.IN_REVIEW,
  startedAt: '2024-01-20T09:00:00Z',
  assignedTo: ['user_1', 'user_2', 'user_3', 'user_4'],
  notifications: [],
  escalations: [],
}

const AcceptanceWorkflowComponent: React.FC<AcceptanceWorkflowProps> = ({
  packageId,
  workflow = mockWorkflow,
  onStepAction,
  onWorkflowUpdate,
  currentUserId = 'user_2',
  readonly = false,
}) => {
  const [actionModalVisible, setActionModalVisible] = useState(false)
  const [selectedStep, setSelectedStep] = useState<AcceptanceStep | null>(null)
  const [selectedAction, setSelectedAction] = useState<any>(null)
  const [commentModalVisible, setCommentModalVisible] = useState(false)
  const [form] = Form.useForm()

  const getStepStatus = (step: AcceptanceStep) => {
    const statusMap = {
      pending: 'wait',
      in_progress: 'process',
      completed: 'finish',
      skipped: 'wait',
      failed: 'error',
    }
    return statusMap[step.status] as any
  }

  const getStepIcon = (step: AcceptanceStep) => {
    const icons = {
      pending: <ClockCircleOutlined />,
      in_progress: <PlayCircleOutlined />,
      completed: <CheckCircleOutlined />,
      skipped: <PauseCircleOutlined />,
      failed: <ExclamationCircleOutlined />,
    }
    return icons[step.status]
  }

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'default',
      in_progress: 'processing',
      completed: 'success',
      skipped: 'warning',
      failed: 'error',
    }
    return colors[status as keyof typeof colors]
  }

  const getStatusText = (status: string) => {
    const texts = {
      pending: '待开始',
      in_progress: '进行中',
      completed: '已完成',
      skipped: '已跳过',
      failed: '失败',
    }
    return texts[status as keyof typeof texts]
  }

  const getDaysRemaining = (dueDate: string) => {
    const days = Math.ceil((new Date(dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    return days
  }

  const isStepAssignedToCurrentUser = (step: AcceptanceStep) => {
    return step.assigneeId === currentUserId
  }

  const canExecuteAction = (step: AcceptanceStep, action: any) => {
    return !readonly && 
           isStepAssignedToCurrentUser(step) && 
           step.status === 'in_progress' && 
           action.isAvailable
  }

  const handleStepAction = (step: AcceptanceStep, action: any) => {
    setSelectedStep(step)
    setSelectedAction(action)
    setActionModalVisible(true)
  }

  const handleActionSubmit = async (values: any) => {
    if (!selectedStep || !selectedAction) return

    try {
      await onStepAction(selectedStep.id, selectedAction.type, {
        comments: values.comments,
        rating: values.rating,
        attachments: values.attachments,
      })
      setActionModalVisible(false)
      form.resetFields()
      message.success(`${selectedAction.name}操作已提交`)
    } catch (error) {
      message.error('操作失败')
    }
  }

  const handleAddComment = (step: AcceptanceStep) => {
    setSelectedStep(step)
    setCommentModalVisible(true)
  }

  const calculateProgress = () => {
    const completedSteps = workflow.steps.filter(step => step.status === 'completed').length
    return Math.round((completedSteps / workflow.steps.length) * 100)
  }

  const currentStep = workflow.steps.find(step => step.id === workflow.currentStepId)
  const currentStepIndex = workflow.steps.findIndex(step => step.id === workflow.currentStepId)

  return (
    <div>
      {/* Workflow Overview */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <div>
            <Title level={4} style={{ margin: 0 }}>{workflow.name}</Title>
            <Text type="secondary">{workflow.description}</Text>
          </div>
          <div style={{ textAlign: 'right' }}>
            <Tag color={getStatusColor(workflow.status)}>
              {getStatusText(workflow.status)}
            </Tag>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              开始于: {new Date(workflow.startedAt).toLocaleString('zh-CN')}
            </Text>
          </div>
        </div>

        <div style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
            <Text strong>整体进度</Text>
            <Text>{workflow.steps.filter(s => s.status === 'completed').length}/{workflow.steps.length} 步骤已完成</Text>
          </div>
          <Progress percent={calculateProgress()} strokeColor="#52c41a" />
        </div>

        {currentStep && (
          <Alert
            message={`当前步骤: ${currentStep.name}`}
            description={
              <div>
                <Text>{currentStep.description}</Text>
                <br />
                <Space style={{ marginTop: 8 }}>
                  <Text type="secondary">负责人: {currentStep.assigneeName}</Text>
                  <Text type="secondary">
                    截止时间: {new Date(currentStep.dueDate).toLocaleString('zh-CN')}
                  </Text>
                  {getDaysRemaining(currentStep.dueDate) <= 1 && (
                    <Tag color="red">即将到期</Tag>
                  )}
                </Space>
              </div>
            }
            type={currentStep.status === 'in_progress' ? 'info' : 'warning'}
            showIcon
          />
        )}
      </Card>

      {/* Steps Progress */}
      <Card title="流程步骤" style={{ marginBottom: 16 }}>
        <Steps current={currentStepIndex} direction="vertical">
          {workflow.steps.map((step, index) => (
            <Step
              key={step.id}
              title={
                <Space>
                  <span>{step.name}</span>
                  <Tag color={getStatusColor(step.status)}>
                    {getStatusText(step.status)}
                  </Tag>
                  {isStepAssignedToCurrentUser(step) && (
                    <Badge dot color="blue">
                      <UserOutlined />
                    </Badge>
                  )}
                </Space>
              }
              description={
                <div>
                  <Text type="secondary">{step.description}</Text>
                  <br />
                  <Space style={{ marginTop: 8 }}>
                    <Avatar size="small" />
                    <Text style={{ fontSize: 12 }}>{step.assigneeName}</Text>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      截止: {new Date(step.dueDate).toLocaleDateString('zh-CN')}
                    </Text>
                    {step.completedAt && (
                      <Text type="success" style={{ fontSize: 12 }}>
                        完成于: {new Date(step.completedAt).toLocaleDateString('zh-CN')}
                      </Text>
                    )}
                  </Space>

                  {/* Requirements and Deliverables */}
                  {step.requirements.length > 0 && (
                    <div style={{ marginTop: 8 }}>
                      <Text strong style={{ fontSize: 12 }}>要求: </Text>
                      <Text style={{ fontSize: 12 }}>{step.requirements.join(', ')}</Text>
                    </div>
                  )}
                  {step.deliverables.length > 0 && (
                    <div style={{ marginTop: 4 }}>
                      <Text strong style={{ fontSize: 12 }}>交付物: </Text>
                      <Text style={{ fontSize: 12 }}>{step.deliverables.join(', ')}</Text>
                    </div>
                  )}

                  {/* Conditions */}
                  {step.conditions.length > 0 && (
                    <div style={{ marginTop: 8 }}>
                      <Text strong style={{ fontSize: 12 }}>条件检查:</Text>
                      {step.conditions.map(condition => (
                        <div key={condition.id} style={{ marginLeft: 16, marginTop: 4 }}>
                          <Space>
                            {condition.isMet ? (
                              <CheckCircleOutlined style={{ color: '#52c41a' }} />
                            ) : (
                              <ExclamationCircleOutlined style={{ color: '#faad14' }} />
                            )}
                            <Text style={{ fontSize: 12 }}>{condition.description}</Text>
                          </Space>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Actions */}
                  {step.status === 'in_progress' && step.actions.length > 0 && (
                    <div style={{ marginTop: 12 }}>
                      <Space>
                        {step.actions.map(action => (
                          <Button
                            key={action.id}
                            type={action.type === 'approve' ? 'primary' : 'default'}
                            size="small"
                            disabled={!canExecuteAction(step, action)}
                            onClick={() => handleStepAction(step, action)}
                          >
                            {action.name}
                          </Button>
                        ))}
                        <Button
                          size="small"
                          icon={<MessageOutlined />}
                          onClick={() => handleAddComment(step)}
                        >
                          添加评论
                        </Button>
                      </Space>
                    </div>
                  )}
                </div>
              }
              status={getStepStatus(step)}
              icon={getStepIcon(step)}
            />
          ))}
        </Steps>
      </Card>

      {/* Timeline */}
      <Card title="操作历史">
        <Timeline>
          <Timeline.Item color="green">
            <Text strong>工作流启动</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              {new Date(workflow.startedAt).toLocaleString('zh-CN')}
            </Text>
          </Timeline.Item>
          
          {workflow.steps
            .filter(step => step.status === 'completed')
            .map(step => (
              <Timeline.Item key={step.id} color="blue">
                <Text strong>{step.name} 已完成</Text>
                <br />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  {step.assigneeName} 于 {new Date(step.completedAt!).toLocaleString('zh-CN')} 完成
                </Text>
              </Timeline.Item>
            ))}

          {currentStep && (
            <Timeline.Item color="orange" dot={<ClockCircleOutlined />}>
              <Text strong>当前步骤: {currentStep.name}</Text>
              <br />
              <Text type="secondary" style={{ fontSize: 12 }}>
                负责人: {currentStep.assigneeName}
              </Text>
            </Timeline.Item>
          )}
        </Timeline>
      </Card>

      {/* Action Modal */}
      <Modal
        title={selectedAction?.name}
        open={actionModalVisible}
        onCancel={() => setActionModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} onFinish={handleActionSubmit} layout="vertical">
          {selectedAction?.type === 'approve' && (
            <Form.Item name="rating" label="评分">
              <Rate />
            </Form.Item>
          )}

          <Form.Item
            name="comments"
            label="备注"
            rules={[{ required: true, message: '请输入备注' }]}
          >
            <TextArea rows={4} placeholder="请输入操作备注..." />
          </Form.Item>
        </Form>
      </Modal>

      {/* Comment Modal */}
      <Modal
        title="添加评论"
        open={commentModalVisible}
        onCancel={() => setCommentModalVisible(false)}
        onOk={() => {
          message.success('评论已添加')
          setCommentModalVisible(false)
        }}
      >
        <Form layout="vertical">
          <Form.Item label="评论内容">
            <TextArea rows={4} placeholder="请输入评论内容..." />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default AcceptanceWorkflowComponent
