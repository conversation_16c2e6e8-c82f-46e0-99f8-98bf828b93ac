import React, { useState } from 'react'
import {
  Card,
  Checkbox,
  List,
  Progress,
  Button,
  Space,
  Typography,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Upload,
  message,
  Collapse,
  Avatar,
  Tooltip,
  Badge,
} from 'antd'
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  FileTextOutlined,
  UserOutlined,
} from '@ant-design/icons'
import { DeliveryChecklistItem } from '../../types/delivery'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select
const { Panel } = Collapse

interface DeliveryChecklistProps {
  packageId: string
  items: DeliveryChecklistItem[]
  onItemUpdate: (itemId: string, updates: Partial<DeliveryChecklistItem>) => void
  onItemCreate: (item: Omit<DeliveryChecklistItem, 'id'>) => void
  onItemDelete: (itemId: string) => void
  readonly?: boolean
}

// Mock checklist items
const mockChecklistItems: DeliveryChecklistItem[] = [
  {
    id: 'item_1',
    packageId: 'package_1',
    category: '代码交付',
    title: '源代码完整性检查',
    description: '确保所有源代码文件已提交，无遗漏',
    isRequired: true,
    isCompleted: true,
    completedBy: '王工程师',
    completedAt: '2024-01-20T10:30:00Z',
    evidence: ['code_review_report.pdf'],
    notes: '代码已通过完整性检查，所有文件已提交',
    order: 1,
  },
  {
    id: 'item_2',
    packageId: 'package_1',
    category: '代码交付',
    title: '代码质量检查',
    description: '代码符合编码规范，通过静态分析',
    isRequired: true,
    isCompleted: true,
    completedBy: '李工程师',
    completedAt: '2024-01-20T14:15:00Z',
    evidence: ['sonar_report.pdf', 'code_quality_metrics.xlsx'],
    notes: '代码质量评分：A级，无严重问题',
    order: 2,
  },
  {
    id: 'item_3',
    packageId: 'package_1',
    category: '测试',
    title: '单元测试覆盖率',
    description: '单元测试覆盖率达到80%以上',
    isRequired: true,
    isCompleted: false,
    order: 3,
  },
  {
    id: 'item_4',
    packageId: 'package_1',
    category: '测试',
    title: '集成测试通过',
    description: '所有集成测试用例执行通过',
    isRequired: true,
    isCompleted: false,
    order: 4,
  },
  {
    id: 'item_5',
    packageId: 'package_1',
    category: '文档',
    title: '用户手册',
    description: '提供完整的用户操作手册',
    isRequired: true,
    isCompleted: true,
    completedBy: '张文档',
    completedAt: '2024-01-19T16:00:00Z',
    evidence: ['user_manual_v1.0.pdf'],
    order: 5,
  },
  {
    id: 'item_6',
    packageId: 'package_1',
    category: '文档',
    title: '技术文档',
    description: '提供系统架构和API文档',
    isRequired: false,
    isCompleted: false,
    order: 6,
  },
  {
    id: 'item_7',
    packageId: 'package_1',
    category: '部署',
    title: '部署脚本',
    description: '提供自动化部署脚本和说明',
    isRequired: true,
    isCompleted: false,
    order: 7,
  },
]

const DeliveryChecklist: React.FC<DeliveryChecklistProps> = ({
  packageId,
  items = mockChecklistItems,
  onItemUpdate,
  onItemCreate,
  onItemDelete,
  readonly = false,
}) => {
  const [selectedItem, setSelectedItem] = useState<DeliveryChecklistItem | null>(null)
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [evidenceModalVisible, setEvidenceModalVisible] = useState(false)
  const [form] = Form.useForm()

  const categories = [...new Set(items.map(item => item.category))]
  const completedItems = items.filter(item => item.isCompleted).length
  const totalItems = items.length
  const requiredItems = items.filter(item => item.isRequired).length
  const completedRequiredItems = items.filter(item => item.isRequired && item.isCompleted).length
  const completionRate = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0
  const requiredCompletionRate = requiredItems > 0 ? Math.round((completedRequiredItems / requiredItems) * 100) : 0

  const handleItemToggle = (item: DeliveryChecklistItem) => {
    if (readonly) return

    if (!item.isCompleted) {
      // Mark as completed
      onItemUpdate(item.id, {
        isCompleted: true,
        completedBy: '当前用户',
        completedAt: new Date().toISOString(),
      })
      message.success(`已完成: ${item.title}`)
    } else {
      // Mark as incomplete
      onItemUpdate(item.id, {
        isCompleted: false,
        completedBy: undefined,
        completedAt: undefined,
      })
      message.info(`已取消完成: ${item.title}`)
    }
  }

  const handleItemEdit = (item: DeliveryChecklistItem) => {
    setSelectedItem(item)
    form.setFieldsValue({
      category: item.category,
      title: item.title,
      description: item.description,
      isRequired: item.isRequired,
      notes: item.notes,
    })
    setEditModalVisible(true)
  }

  const handleItemUpdate = async (values: any) => {
    if (!selectedItem) return

    try {
      onItemUpdate(selectedItem.id, {
        category: values.category,
        title: values.title,
        description: values.description,
        isRequired: values.isRequired,
        notes: values.notes,
      })
      setEditModalVisible(false)
      form.resetFields()
      message.success('检查项已更新')
    } catch (error) {
      message.error('更新失败')
    }
  }

  const handleItemCreate = async (values: any) => {
    try {
      const newItem = {
        packageId,
        category: values.category,
        title: values.title,
        description: values.description,
        isRequired: values.isRequired,
        isCompleted: false,
        order: items.length + 1,
      }
      onItemCreate(newItem)
      setCreateModalVisible(false)
      form.resetFields()
      message.success('检查项已创建')
    } catch (error) {
      message.error('创建失败')
    }
  }

  const handleItemDelete = (itemId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个检查项吗？',
      onOk: () => {
        onItemDelete(itemId)
        message.success('检查项已删除')
      },
    })
  }

  const renderChecklistItem = (item: DeliveryChecklistItem) => (
    <List.Item
      key={item.id}
      actions={[
        <Tooltip title="查看证据">
          <Button
            type="text"
            icon={<FileTextOutlined />}
            onClick={() => {
              setSelectedItem(item)
              setEvidenceModalVisible(true)
            }}
            disabled={!item.evidence || item.evidence.length === 0}
          />
        </Tooltip>,
        !readonly && (
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleItemEdit(item)}
          />
        ),
        !readonly && (
          <Button
            type="text"
            icon={<DeleteOutlined />}
            danger
            onClick={() => handleItemDelete(item.id)}
          />
        ),
      ].filter(Boolean)}
    >
      <List.Item.Meta
        avatar={
          <Checkbox
            checked={item.isCompleted}
            onChange={() => handleItemToggle(item)}
            disabled={readonly}
          />
        }
        title={
          <Space>
            <span style={{ textDecoration: item.isCompleted ? 'line-through' : 'none' }}>
              {item.title}
            </span>
            {item.isRequired && <Tag color="red">必需</Tag>}
            {item.isCompleted && <CheckCircleOutlined style={{ color: '#52c41a' }} />}
          </Space>
        }
        description={
          <div>
            <Text type="secondary">{item.description}</Text>
            {item.isCompleted && item.completedBy && (
              <div style={{ marginTop: 4 }}>
                <Space>
                  <Avatar size="small" icon={<UserOutlined />} />
                  <Text style={{ fontSize: 12 }}>
                    {item.completedBy} 于 {new Date(item.completedAt!).toLocaleString('zh-CN')} 完成
                  </Text>
                </Space>
              </div>
            )}
            {item.notes && (
              <div style={{ marginTop: 4 }}>
                <Text style={{ fontSize: 12, fontStyle: 'italic' }}>
                  备注: {item.notes}
                </Text>
              </div>
            )}
            {item.evidence && item.evidence.length > 0 && (
              <div style={{ marginTop: 4 }}>
                <Space>
                  <FileTextOutlined style={{ fontSize: 12 }} />
                  <Text style={{ fontSize: 12 }}>
                    {item.evidence.length} 个证据文件
                  </Text>
                </Space>
              </div>
            )}
          </div>
        }
      />
    </List.Item>
  )

  return (
    <div>
      {/* Progress Overview */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <Text strong>总体进度</Text>
                <Text>{completedItems}/{totalItems} 项已完成</Text>
              </div>
              <Progress
                percent={completionRate}
                strokeColor="#52c41a"
                trailColor="#f0f0f0"
              />
            </div>
          </Col>
          <Col xs={24} md={12}>
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <Text strong>必需项进度</Text>
                <Text>{completedRequiredItems}/{requiredItems} 项已完成</Text>
              </div>
              <Progress
                percent={requiredCompletionRate}
                strokeColor={requiredCompletionRate === 100 ? "#52c41a" : "#faad14"}
                trailColor="#f0f0f0"
              />
            </div>
          </Col>
        </Row>

        {requiredCompletionRate < 100 && (
          <div style={{ marginTop: 16 }}>
            <Badge status="warning" text="还有必需项未完成，无法进行交付" />
          </div>
        )}
      </Card>

      {/* Checklist by Category */}
      <Card
        title="交付检查清单"
        extra={
          !readonly && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              添加检查项
            </Button>
          )
        }
      >
        <Collapse defaultActiveKey={categories}>
          {categories.map(category => {
            const categoryItems = items.filter(item => item.category === category)
            const categoryCompleted = categoryItems.filter(item => item.isCompleted).length
            const categoryTotal = categoryItems.length

            return (
              <Panel
                header={
                  <Space>
                    <span>{category}</span>
                    <Tag>{categoryCompleted}/{categoryTotal}</Tag>
                    <Progress
                      percent={Math.round((categoryCompleted / categoryTotal) * 100)}
                      size="small"
                      style={{ width: 100 }}
                    />
                  </Space>
                }
                key={category}
              >
                <List
                  dataSource={categoryItems.sort((a, b) => a.order - b.order)}
                  renderItem={renderChecklistItem}
                />
              </Panel>
            )
          })}
        </Collapse>
      </Card>

      {/* Edit Item Modal */}
      <Modal
        title="编辑检查项"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} onFinish={handleItemUpdate} layout="vertical">
          <Form.Item
            name="category"
            label="分类"
            rules={[{ required: true, message: '请输入分类' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea rows={3} />
          </Form.Item>

          <Form.Item name="isRequired" label="是否必需" valuePropName="checked">
            <Checkbox>必需项</Checkbox>
          </Form.Item>

          <Form.Item name="notes" label="备注">
            <TextArea rows={2} />
          </Form.Item>
        </Form>
      </Modal>

      {/* Create Item Modal */}
      <Modal
        title="创建检查项"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} onFinish={handleItemCreate} layout="vertical">
          <Form.Item
            name="category"
            label="分类"
            rules={[{ required: true, message: '请输入分类' }]}
          >
            <Select
              placeholder="选择或输入分类"
              allowClear
              showSearch
              optionFilterProp="children"
            >
              {categories.map(cat => (
                <Option key={cat} value={cat}>{cat}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea rows={3} />
          </Form.Item>

          <Form.Item name="isRequired" label="是否必需" valuePropName="checked">
            <Checkbox>必需项</Checkbox>
          </Form.Item>
        </Form>
      </Modal>

      {/* Evidence Modal */}
      <Modal
        title="证据文件"
        open={evidenceModalVisible}
        onCancel={() => setEvidenceModalVisible(false)}
        footer={null}
      >
        {selectedItem && (
          <div>
            <Title level={5}>{selectedItem.title}</Title>
            {selectedItem.evidence && selectedItem.evidence.length > 0 ? (
              <List
                dataSource={selectedItem.evidence}
                renderItem={file => (
                  <List.Item
                    actions={[
                      <Button type="link">下载</Button>,
                      <Button type="link">预览</Button>,
                    ]}
                  >
                    <List.Item.Meta
                      avatar={<FileTextOutlined />}
                      title={file}
                      description="证据文件"
                    />
                  </List.Item>
                )}
              />
            ) : (
              <Text type="secondary">暂无证据文件</Text>
            )}

            {!readonly && (
              <div style={{ marginTop: 16 }}>
                <Upload>
                  <Button icon={<UploadOutlined />}>上传证据文件</Button>
                </Upload>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}

export default DeliveryChecklist
