import React, { useState } from 'react'
import {
  Card,
  Upload,
  Button,
  List,
  Space,
  Typography,
  Tag,
  Modal,
  Progress,
  Image,
  Tooltip,
  message,
  Popconfirm,
  Input,
  Select,
  Row,
  Col,
} from 'antd'
import {
  UploadOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  FileVideoOutlined,
  FileZipOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  FilePptOutlined,
  ShareAltOutlined,
  HistoryOutlined,
} from '@ant-design/icons'
import { DeliverableFile, FilePreview, FileVersion } from '../../types/delivery'
import type { UploadProps, UploadFile } from 'antd/es/upload/interface'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

interface FileUploadPreviewProps {
  deliverableId: string
  files: DeliverableFile[]
  onFileUpload: (file: File, metadata: any) => Promise<void>
  onFileDelete: (fileId: string) => void
  onFileUpdate: (fileId: string, updates: Partial<DeliverableFile>) => void
  onFileShare: (fileId: string, shareSettings: any) => void
  readonly?: boolean
  maxFileSize?: number // MB
  allowedTypes?: string[]
}

// Mock files data
const mockFiles: DeliverableFile[] = [
  {
    id: 'file_1',
    deliverableId: 'deliverable_1',
    name: 'system_architecture.pdf',
    originalName: '系统架构设计文档.pdf',
    path: '/files/system_architecture.pdf',
    url: 'https://example.com/files/system_architecture.pdf',
    size: 2048576,
    type: 'pdf',
    mimeType: 'application/pdf',
    checksum: 'abc123def456',
    version: '1.0',
    isMainFile: true,
    previewUrl: 'https://example.com/preview/system_architecture.pdf',
    thumbnailUrl: 'https://example.com/thumb/system_architecture.pdf',
    downloadCount: 5,
    uploadedBy: '张工程师',
    uploadedAt: '2024-01-20T10:00:00Z',
    lastModified: '2024-01-20T10:00:00Z',
    metadata: {
      pageCount: 25,
      author: '张工程师',
      title: '系统架构设计文档',
    },
  },
  {
    id: 'file_2',
    deliverableId: 'deliverable_1',
    name: 'source_code.zip',
    originalName: '源代码包.zip',
    path: '/files/source_code.zip',
    url: 'https://example.com/files/source_code.zip',
    size: 15728640,
    type: 'zip',
    mimeType: 'application/zip',
    checksum: 'def456ghi789',
    version: '2.1',
    isMainFile: false,
    downloadCount: 12,
    uploadedBy: '李工程师',
    uploadedAt: '2024-01-21T14:30:00Z',
    lastModified: '2024-01-21T14:30:00Z',
    metadata: {
      compression: 'zip',
      fileCount: 156,
    },
  },
  {
    id: 'file_3',
    deliverableId: 'deliverable_1',
    name: 'demo_video.mp4',
    originalName: '系统演示视频.mp4',
    path: '/files/demo_video.mp4',
    url: 'https://example.com/files/demo_video.mp4',
    size: 52428800,
    type: 'video',
    mimeType: 'video/mp4',
    checksum: 'ghi789jkl012',
    version: '1.0',
    isMainFile: false,
    previewUrl: 'https://example.com/preview/demo_video.mp4',
    thumbnailUrl: 'https://example.com/thumb/demo_video.jpg',
    downloadCount: 8,
    uploadedBy: '王设计师',
    uploadedAt: '2024-01-22T09:15:00Z',
    lastModified: '2024-01-22T09:15:00Z',
    metadata: {
      duration: 180,
      resolution: '1920x1080',
      bitrate: '2000kbps',
    },
  },
]

const FileUploadPreview: React.FC<FileUploadPreviewProps> = ({
  deliverableId,
  files = mockFiles,
  onFileUpload,
  onFileDelete,
  onFileUpdate,
  onFileShare,
  readonly = false,
  maxFileSize = 100,
  allowedTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'zip', 'rar', 'jpg', 'png', 'mp4', 'avi'],
}) => {
  const [uploading, setUploading] = useState(false)
  const [previewVisible, setPreviewVisible] = useState(false)
  const [previewFile, setPreviewFile] = useState<DeliverableFile | null>(null)
  const [shareModalVisible, setShareModalVisible] = useState(false)
  const [versionModalVisible, setVersionModalVisible] = useState(false)
  const [selectedFile, setSelectedFile] = useState<DeliverableFile | null>(null)
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({})

  const getFileIcon = (type: string) => {
    const icons = {
      pdf: <FilePdfOutlined style={{ color: '#ff4d4f' }} />,
      doc: <FileWordOutlined style={{ color: '#1890ff' }} />,
      docx: <FileWordOutlined style={{ color: '#1890ff' }} />,
      xls: <FileExcelOutlined style={{ color: '#52c41a' }} />,
      xlsx: <FileExcelOutlined style={{ color: '#52c41a' }} />,
      ppt: <FilePptOutlined style={{ color: '#faad14' }} />,
      pptx: <FilePptOutlined style={{ color: '#faad14' }} />,
      zip: <FileZipOutlined style={{ color: '#722ed1' }} />,
      rar: <FileZipOutlined style={{ color: '#722ed1' }} />,
      jpg: <FileImageOutlined style={{ color: '#13c2c2' }} />,
      jpeg: <FileImageOutlined style={{ color: '#13c2c2' }} />,
      png: <FileImageOutlined style={{ color: '#13c2c2' }} />,
      gif: <FileImageOutlined style={{ color: '#13c2c2' }} />,
      mp4: <FileVideoOutlined style={{ color: '#eb2f96' }} />,
      avi: <FileVideoOutlined style={{ color: '#eb2f96' }} />,
      mov: <FileVideoOutlined style={{ color: '#eb2f96' }} />,
    }
    return icons[type as keyof typeof icons] || <FileTextOutlined />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const isImageFile = (type: string) => {
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(type.toLowerCase())
  }

  const isVideoFile = (type: string) => {
    return ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(type.toLowerCase())
  }

  const handleUpload = async (file: File) => {
    if (readonly) return false

    // Validate file size
    if (file.size > maxFileSize * 1024 * 1024) {
      message.error(`文件大小不能超过 ${maxFileSize}MB`)
      return false
    }

    // Validate file type
    const fileExtension = file.name.split('.').pop()?.toLowerCase()
    if (fileExtension && !allowedTypes.includes(fileExtension)) {
      message.error(`不支持的文件类型: ${fileExtension}`)
      return false
    }

    setUploading(true)
    setUploadProgress(prev => ({ ...prev, [file.name]: 0 }))

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const currentProgress = prev[file.name] || 0
          if (currentProgress >= 100) {
            clearInterval(progressInterval)
            return prev
          }
          return { ...prev, [file.name]: currentProgress + 10 }
        })
      }, 200)

      await onFileUpload(file, {
        deliverableId,
        originalName: file.name,
        type: fileExtension,
        mimeType: file.type,
      })

      clearInterval(progressInterval)
      setUploadProgress(prev => ({ ...prev, [file.name]: 100 }))
      message.success('文件上传成功')
    } catch (error) {
      message.error('文件上传失败')
    } finally {
      setUploading(false)
      setTimeout(() => {
        setUploadProgress(prev => {
          const newProgress = { ...prev }
          delete newProgress[file.name]
          return newProgress
        })
      }, 1000)
    }

    return false // Prevent default upload behavior
  }

  const handlePreview = (file: DeliverableFile) => {
    setPreviewFile(file)
    setPreviewVisible(true)
  }

  const handleDownload = (file: DeliverableFile) => {
    // Simulate download
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.originalName
    link.click()
    
    // Update download count
    onFileUpdate(file.id, {
      downloadCount: file.downloadCount + 1,
    })
    message.success('开始下载文件')
  }

  const handleShare = (file: DeliverableFile) => {
    setSelectedFile(file)
    setShareModalVisible(true)
  }

  const handleVersionHistory = (file: DeliverableFile) => {
    setSelectedFile(file)
    setVersionModalVisible(true)
  }

  const renderFilePreview = () => {
    if (!previewFile) return null

    if (isImageFile(previewFile.type)) {
      return (
        <Image
          src={previewFile.url}
          alt={previewFile.originalName}
          style={{ maxWidth: '100%', maxHeight: '500px' }}
        />
      )
    }

    if (isVideoFile(previewFile.type)) {
      return (
        <video
          controls
          style={{ maxWidth: '100%', maxHeight: '500px' }}
          src={previewFile.url}
        >
          您的浏览器不支持视频播放
        </video>
      )
    }

    if (previewFile.type === 'pdf' && previewFile.previewUrl) {
      return (
        <iframe
          src={previewFile.previewUrl}
          style={{ width: '100%', height: '500px', border: 'none' }}
          title={previewFile.originalName}
        />
      )
    }

    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <FileTextOutlined style={{ fontSize: '64px', color: '#ccc' }} />
        <div style={{ marginTop: '16px' }}>
          <Text type="secondary">此文件类型不支持预览</Text>
          <br />
          <Button type="link" onClick={() => handleDownload(previewFile)}>
            点击下载查看
          </Button>
        </div>
      </div>
    )
  }

  const uploadProps: UploadProps = {
    beforeUpload: handleUpload,
    showUploadList: false,
    multiple: true,
    disabled: readonly || uploading,
  }

  return (
    <div>
      {/* Upload Area */}
      {!readonly && (
        <Card style={{ marginBottom: 16 }}>
          <Upload.Dragger {...uploadProps}>
            <p className="ant-upload-drag-icon">
              <UploadOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持单个或批量上传。文件大小限制: {maxFileSize}MB
              <br />
              支持格式: {allowedTypes.join(', ')}
            </p>
          </Upload.Dragger>

          {/* Upload Progress */}
          {Object.keys(uploadProgress).length > 0 && (
            <div style={{ marginTop: 16 }}>
              {Object.entries(uploadProgress).map(([fileName, progress]) => (
                <div key={fileName} style={{ marginBottom: 8 }}>
                  <Text>{fileName}</Text>
                  <Progress percent={progress} size="small" />
                </div>
              ))}
            </div>
          )}
        </Card>
      )}

      {/* File List */}
      <Card title={`文件列表 (${files.length})`}>
        <List
          dataSource={files}
          renderItem={(file) => (
            <List.Item
              actions={[
                <Tooltip title="预览">
                  <Button
                    type="text"
                    icon={<EyeOutlined />}
                    onClick={() => handlePreview(file)}
                  />
                </Tooltip>,
                <Tooltip title="下载">
                  <Button
                    type="text"
                    icon={<DownloadOutlined />}
                    onClick={() => handleDownload(file)}
                  />
                </Tooltip>,
                <Tooltip title="分享">
                  <Button
                    type="text"
                    icon={<ShareAltOutlined />}
                    onClick={() => handleShare(file)}
                  />
                </Tooltip>,
                <Tooltip title="版本历史">
                  <Button
                    type="text"
                    icon={<HistoryOutlined />}
                    onClick={() => handleVersionHistory(file)}
                  />
                </Tooltip>,
                !readonly && (
                  <Popconfirm
                    title="确定要删除这个文件吗？"
                    onConfirm={() => onFileDelete(file.id)}
                  >
                    <Button
                      type="text"
                      icon={<DeleteOutlined />}
                      danger
                    />
                  </Popconfirm>
                ),
              ].filter(Boolean)}
            >
              <List.Item.Meta
                avatar={getFileIcon(file.type)}
                title={
                  <Space>
                    <span>{file.originalName}</span>
                    {file.isMainFile && <Tag color="blue">主文件</Tag>}
                    <Tag>{file.version}</Tag>
                  </Space>
                }
                description={
                  <div>
                    <Space>
                      <Text type="secondary">{formatFileSize(file.size)}</Text>
                      <Text type="secondary">•</Text>
                      <Text type="secondary">
                        {file.uploadedBy} 上传于 {new Date(file.uploadedAt).toLocaleString('zh-CN')}
                      </Text>
                      <Text type="secondary">•</Text>
                      <Text type="secondary">下载 {file.downloadCount} 次</Text>
                    </Space>
                    {file.metadata && (
                      <div style={{ marginTop: 4 }}>
                        <Space size="small">
                          {file.metadata.pageCount && (
                            <Tag size="small">{file.metadata.pageCount} 页</Tag>
                          )}
                          {file.metadata.duration && (
                            <Tag size="small">{Math.round(file.metadata.duration / 60)} 分钟</Tag>
                          )}
                          {file.metadata.resolution && (
                            <Tag size="small">{file.metadata.resolution}</Tag>
                          )}
                          {file.metadata.fileCount && (
                            <Tag size="small">{file.metadata.fileCount} 个文件</Tag>
                          )}
                        </Space>
                      </div>
                    )}
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Card>

      {/* Preview Modal */}
      <Modal
        title={previewFile?.originalName}
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="download" onClick={() => previewFile && handleDownload(previewFile)}>
            下载
          </Button>,
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>,
        ]}
        width="80%"
        style={{ top: 20 }}
      >
        {renderFilePreview()}
      </Modal>

      {/* Share Modal */}
      <Modal
        title="分享文件"
        open={shareModalVisible}
        onCancel={() => setShareModalVisible(false)}
        onOk={() => {
          message.success('分享设置已保存')
          setShareModalVisible(false)
        }}
      >
        {selectedFile && (
          <div>
            <Title level={5}>{selectedFile.originalName}</Title>
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>分享给:</Text>
                <Select
                  mode="multiple"
                  style={{ width: '100%', marginTop: 8 }}
                  placeholder="选择用户或输入邮箱"
                >
                  <Option value="user1">张三</Option>
                  <Option value="user2">李四</Option>
                  <Option value="user3">王五</Option>
                </Select>
              </Col>
              <Col span={12}>
                <Text strong>权限:</Text>
                <Select
                  defaultValue="view"
                  style={{ width: '100%', marginTop: 8 }}
                >
                  <Option value="view">仅查看</Option>
                  <Option value="download">查看和下载</Option>
                  <Option value="comment">查看、下载和评论</Option>
                </Select>
              </Col>
            </Row>
            <div style={{ marginTop: 16 }}>
              <Text strong>有效期:</Text>
              <Select
                defaultValue="7"
                style={{ width: '100%', marginTop: 8 }}
              >
                <Option value="1">1天</Option>
                <Option value="7">7天</Option>
                <Option value="30">30天</Option>
                <Option value="0">永久</Option>
              </Select>
            </div>
          </div>
        )}
      </Modal>

      {/* Version History Modal */}
      <Modal
        title="版本历史"
        open={versionModalVisible}
        onCancel={() => setVersionModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedFile && (
          <div>
            <Title level={5}>{selectedFile.originalName}</Title>
            <List
              dataSource={[
                {
                  version: '2.1',
                  size: selectedFile.size,
                  uploadedBy: selectedFile.uploadedBy,
                  uploadedAt: selectedFile.uploadedAt,
                  changes: '修复了文档中的错误，更新了API说明',
                  isActive: true,
                },
                {
                  version: '2.0',
                  size: selectedFile.size - 1024,
                  uploadedBy: '李工程师',
                  uploadedAt: '2024-01-20T10:00:00Z',
                  changes: '添加了新的章节，重构了部分内容',
                  isActive: false,
                },
                {
                  version: '1.0',
                  size: selectedFile.size - 2048,
                  uploadedBy: '张工程师',
                  uploadedAt: '2024-01-19T15:30:00Z',
                  changes: '初始版本',
                  isActive: false,
                },
              ]}
              renderItem={(version) => (
                <List.Item
                  actions={[
                    <Button type="link" size="small">下载</Button>,
                    <Button type="link" size="small">对比</Button>,
                    !version.isActive && (
                      <Button type="link" size="small">恢复</Button>
                    ),
                  ].filter(Boolean)}
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <span>v{version.version}</span>
                        {version.isActive && <Tag color="green">当前版本</Tag>}
                      </Space>
                    }
                    description={
                      <div>
                        <Text type="secondary">
                          {formatFileSize(version.size)} • {version.uploadedBy} • {new Date(version.uploadedAt).toLocaleString('zh-CN')}
                        </Text>
                        <br />
                        <Text style={{ fontSize: 12 }}>{version.changes}</Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </div>
        )}
      </Modal>
    </div>
  )
}

export default FileUploadPreview
