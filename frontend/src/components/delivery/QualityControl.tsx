import React, { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Progress,
  Rate,
  Button,
  Space,
  Typography,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Table,
  List,
  Alert,
  Statistic,
  Tooltip,
  Badge,
  message,
} from 'antd'
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  TrophyOutlined,
  BugOutlined,
  FileTextOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons'
import { QualityReport, QualityCriteria, QualityIssue, QualityLevel } from '../../types/delivery'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

interface QualityControlProps {
  packageId: string
  qualityReport: QualityReport
  onReportUpdate: (updates: Partial<QualityReport>) => void
  onCriteriaUpdate: (criteriaId: string, updates: Partial<QualityCriteria>) => void
  onIssueCreate: (issue: Omit<QualityIssue, 'id'>) => void
  onIssueUpdate: (issueId: string, updates: Partial<QualityIssue>) => void
  readonly?: boolean
}

// Mock quality report data
const mockQualityReport: QualityReport = {
  id: 'report_1',
  packageId: 'package_1',
  overallScore: 85,
  overallLevel: QualityLevel.GOOD,
  criteria: [
    {
      id: 'criteria_1',
      name: '代码质量',
      description: '代码规范性、可维护性、性能等',
      weight: 30,
      score: 88,
      maxScore: 100,
      level: QualityLevel.GOOD,
      evidence: ['sonar_report.pdf', 'code_review_checklist.xlsx'],
      comments: '代码整体质量良好，符合编码规范，有少量优化空间',
      reviewedBy: '李技术经理',
      reviewedAt: '2024-01-22T14:30:00Z',
    },
    {
      id: 'criteria_2',
      name: '功能完整性',
      description: '功能实现的完整性和正确性',
      weight: 25,
      score: 92,
      maxScore: 100,
      level: QualityLevel.EXCELLENT,
      evidence: ['test_report.pdf', 'feature_checklist.xlsx'],
      comments: '所有功能均已实现且测试通过',
      reviewedBy: '王测试经理',
      reviewedAt: '2024-01-22T16:00:00Z',
    },
    {
      id: 'criteria_3',
      name: '用户体验',
      description: '界面设计、交互体验、易用性',
      weight: 20,
      score: 78,
      maxScore: 100,
      level: QualityLevel.SATISFACTORY,
      evidence: ['ux_review.pdf', 'user_feedback.xlsx'],
      comments: '界面设计基本符合要求，部分交互可以优化',
      reviewedBy: '张UX设计师',
      reviewedAt: '2024-01-22T10:15:00Z',
    },
    {
      id: 'criteria_4',
      name: '性能表现',
      description: '系统响应速度、并发处理能力',
      weight: 15,
      score: 82,
      maxScore: 100,
      level: QualityLevel.GOOD,
      evidence: ['performance_test.pdf'],
      comments: '性能指标基本达标，建议优化数据库查询',
      reviewedBy: '赵性能工程师',
      reviewedAt: '2024-01-22T18:20:00Z',
    },
    {
      id: 'criteria_5',
      name: '文档质量',
      description: '技术文档、用户手册的完整性和准确性',
      weight: 10,
      score: 90,
      maxScore: 100,
      level: QualityLevel.EXCELLENT,
      evidence: ['doc_review.pdf'],
      comments: '文档详细完整，格式规范',
      reviewedBy: '陈文档专员',
      reviewedAt: '2024-01-22T12:45:00Z',
    },
  ],
  issues: [
    {
      id: 'issue_1',
      title: '登录页面响应速度慢',
      description: '用户登录时页面加载时间超过3秒，影响用户体验',
      severity: 'medium',
      category: 'performance',
      status: 'open',
      assigneeId: 'user_1',
      assigneeName: '王前端工程师',
      dueDate: '2024-01-25T18:00:00Z',
      attachments: ['login_performance.png'],
      createdBy: '张测试员',
      createdAt: '2024-01-22T09:30:00Z',
      updatedAt: '2024-01-22T09:30:00Z',
    },
    {
      id: 'issue_2',
      title: '数据导出功能缺少进度提示',
      description: '大量数据导出时用户无法看到进度，体验不佳',
      severity: 'low',
      category: 'usability',
      status: 'in_progress',
      assigneeId: 'user_2',
      assigneeName: '李前端工程师',
      resolution: '已添加进度条组件，正在测试中',
      attachments: [],
      createdBy: '张测试员',
      createdAt: '2024-01-21T14:20:00Z',
      updatedAt: '2024-01-22T16:10:00Z',
    },
  ],
  recommendations: [
    '建议优化数据库查询性能，特别是用户登录相关的查询',
    '可以考虑添加更多的用户引导和帮助信息',
    '建议增加单元测试覆盖率到90%以上',
  ],
  reviewedBy: '质量保证部',
  reviewedAt: '2024-01-22T20:00:00Z',
  summary: '整体质量良好，达到交付标准。存在少量性能和用户体验问题，建议在正式交付前修复。',
}

const QualityControl: React.FC<QualityControlProps> = ({
  packageId,
  qualityReport = mockQualityReport,
  onReportUpdate,
  onCriteriaUpdate,
  onIssueCreate,
  onIssueUpdate,
  readonly = false,
}) => {
  const [issueModalVisible, setIssueModalVisible] = useState(false)
  const [criteriaModalVisible, setCriteriaModalVisible] = useState(false)
  const [selectedIssue, setSelectedIssue] = useState<QualityIssue | null>(null)
  const [selectedCriteria, setSelectedCriteria] = useState<QualityCriteria | null>(null)
  const [form] = Form.useForm()

  const getQualityLevelColor = (level: QualityLevel) => {
    const colors = {
      [QualityLevel.EXCELLENT]: '#52c41a',
      [QualityLevel.GOOD]: '#1890ff',
      [QualityLevel.SATISFACTORY]: '#faad14',
      [QualityLevel.NEEDS_IMPROVEMENT]: '#fa8c16',
      [QualityLevel.POOR]: '#ff4d4f',
    }
    return colors[level]
  }

  const getQualityLevelText = (level: QualityLevel) => {
    const texts = {
      [QualityLevel.EXCELLENT]: '优秀',
      [QualityLevel.GOOD]: '良好',
      [QualityLevel.SATISFACTORY]: '满意',
      [QualityLevel.NEEDS_IMPROVEMENT]: '需改进',
      [QualityLevel.POOR]: '差',
    }
    return texts[level]
  }

  const getSeverityColor = (severity: string) => {
    const colors = {
      low: 'green',
      medium: 'orange',
      high: 'red',
      critical: 'purple',
    }
    return colors[severity as keyof typeof colors]
  }

  const getSeverityText = (severity: string) => {
    const texts = {
      low: '低',
      medium: '中',
      high: '高',
      critical: '严重',
    }
    return texts[severity as keyof typeof texts]
  }

  const getStatusColor = (status: string) => {
    const colors = {
      open: 'red',
      in_progress: 'orange',
      resolved: 'green',
      closed: 'default',
    }
    return colors[status as keyof typeof colors]
  }

  const getStatusText = (status: string) => {
    const texts = {
      open: '待处理',
      in_progress: '处理中',
      resolved: '已解决',
      closed: '已关闭',
    }
    return texts[status as keyof typeof texts]
  }

  const calculateWeightedScore = () => {
    const totalWeight = qualityReport.criteria.reduce((sum, criteria) => sum + criteria.weight, 0)
    const weightedSum = qualityReport.criteria.reduce((sum, criteria) => {
      return sum + (criteria.score * criteria.weight / criteria.maxScore)
    }, 0)
    return Math.round(weightedSum / totalWeight * 100)
  }

  const handleIssueCreate = async (values: any) => {
    try {
      const newIssue = {
        packageId,
        title: values.title,
        description: values.description,
        severity: values.severity,
        category: values.category,
        status: 'open' as const,
        assigneeId: values.assigneeId,
        assigneeName: values.assigneeName,
        dueDate: values.dueDate?.toISOString(),
        attachments: [],
        createdBy: '当前用户',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
      onIssueCreate(newIssue)
      setIssueModalVisible(false)
      form.resetFields()
      message.success('质量问题已创建')
    } catch (error) {
      message.error('创建失败')
    }
  }

  const handleIssueUpdate = async (values: any) => {
    if (!selectedIssue) return

    try {
      onIssueUpdate(selectedIssue.id, {
        status: values.status,
        resolution: values.resolution,
        resolvedBy: values.status === 'resolved' ? '当前用户' : undefined,
        resolvedAt: values.status === 'resolved' ? new Date().toISOString() : undefined,
        updatedAt: new Date().toISOString(),
      })
      setIssueModalVisible(false)
      form.resetFields()
      message.success('问题状态已更新')
    } catch (error) {
      message.error('更新失败')
    }
  }

  const handleCriteriaEdit = (criteria: QualityCriteria) => {
    setSelectedCriteria(criteria)
    form.setFieldsValue({
      score: criteria.score,
      comments: criteria.comments,
    })
    setCriteriaModalVisible(true)
  }

  const handleCriteriaUpdate = async (values: any) => {
    if (!selectedCriteria) return

    try {
      const newLevel = values.score >= 90 ? QualityLevel.EXCELLENT :
                      values.score >= 80 ? QualityLevel.GOOD :
                      values.score >= 70 ? QualityLevel.SATISFACTORY :
                      values.score >= 60 ? QualityLevel.NEEDS_IMPROVEMENT :
                      QualityLevel.POOR

      onCriteriaUpdate(selectedCriteria.id, {
        score: values.score,
        level: newLevel,
        comments: values.comments,
        reviewedBy: '当前用户',
        reviewedAt: new Date().toISOString(),
      })
      setCriteriaModalVisible(false)
      form.resetFields()
      message.success('评分已更新')
    } catch (error) {
      message.error('更新失败')
    }
  }

  const issueColumns: ColumnsType<QualityIssue> = [
    {
      title: '问题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.description}
          </Text>
        </div>
      ),
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      render: (severity) => (
        <Tag color={getSeverityColor(severity)}>
          {getSeverityText(severity)}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '负责人',
      dataIndex: 'assigneeName',
      key: 'assigneeName',
    },
    {
      title: '截止日期',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (date) => date ? new Date(date).toLocaleDateString('zh-CN') : '-',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            size="small"
            onClick={() => {
              setSelectedIssue(record)
              form.setFieldsValue({
                status: record.status,
                resolution: record.resolution,
              })
              setIssueModalVisible(true)
            }}
          >
            更新
          </Button>
        </Space>
      ),
    },
  ]

  const criticalIssues = qualityReport.issues.filter(issue => issue.severity === 'critical').length
  const openIssues = qualityReport.issues.filter(issue => issue.status === 'open').length

  return (
    <div>
      {/* Quality Overview */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总体评分"
              value={qualityReport.overallScore}
              suffix="/100"
              prefix={<TrophyOutlined />}
              valueStyle={{ color: getQualityLevelColor(qualityReport.overallLevel) }}
            />
            <div style={{ marginTop: 8 }}>
              <Tag color={getQualityLevelColor(qualityReport.overallLevel)}>
                {getQualityLevelText(qualityReport.overallLevel)}
              </Tag>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="质量问题"
              value={qualityReport.issues.length}
              suffix="个"
              prefix={<BugOutlined />}
              valueStyle={{ color: qualityReport.issues.length > 0 ? '#ff4d4f' : '#52c41a' }}
            />
            {criticalIssues > 0 && (
              <div style={{ marginTop: 8 }}>
                <Tag color="red">{criticalIssues} 个严重问题</Tag>
              </div>
            )}
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="待处理问题"
              value={openIssues}
              suffix="个"
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: openIssues > 0 ? '#faad14' : '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="评审完成度"
              value={Math.round((qualityReport.criteria.length / 5) * 100)}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Quality Alerts */}
      {criticalIssues > 0 && (
        <Alert
          message="严重质量问题"
          description={`发现 ${criticalIssues} 个严重质量问题，建议在交付前解决`}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {qualityReport.overallScore < 70 && (
        <Alert
          message="质量评分偏低"
          description="总体质量评分低于70分，建议进行质量改进"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* Quality Criteria */}
      <Card title="质量评估标准" style={{ marginBottom: 16 }}>
        <List
          dataSource={qualityReport.criteria}
          renderItem={(criteria) => (
            <List.Item
              actions={[
                !readonly && (
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    onClick={() => handleCriteriaEdit(criteria)}
                  >
                    编辑评分
                  </Button>
                ),
              ].filter(Boolean)}
            >
              <List.Item.Meta
                title={
                  <Space>
                    <span>{criteria.name}</span>
                    <Tag color={getQualityLevelColor(criteria.level)}>
                      {getQualityLevelText(criteria.level)}
                    </Tag>
                    <Text type="secondary">权重: {criteria.weight}%</Text>
                  </Space>
                }
                description={
                  <div>
                    <Text type="secondary">{criteria.description}</Text>
                    <div style={{ marginTop: 8 }}>
                      <Progress
                        percent={Math.round((criteria.score / criteria.maxScore) * 100)}
                        strokeColor={getQualityLevelColor(criteria.level)}
                        format={() => `${criteria.score}/${criteria.maxScore}`}
                      />
                    </div>
                    <div style={{ marginTop: 8 }}>
                      <Text style={{ fontSize: 12 }}>{criteria.comments}</Text>
                    </div>
                    <div style={{ marginTop: 4 }}>
                      <Space>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          评审人: {criteria.reviewedBy}
                        </Text>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          时间: {new Date(criteria.reviewedAt).toLocaleString('zh-CN')}
                        </Text>
                      </Space>
                    </div>
                    {criteria.evidence.length > 0 && (
                      <div style={{ marginTop: 4 }}>
                        <Space>
                          <FileTextOutlined style={{ fontSize: 12 }} />
                          <Text style={{ fontSize: 12 }}>
                            证据文件: {criteria.evidence.join(', ')}
                          </Text>
                        </Space>
                      </div>
                    )}
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Card>

      {/* Quality Issues */}
      <Card
        title="质量问题"
        extra={
          !readonly && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setSelectedIssue(null)
                form.resetFields()
                setIssueModalVisible(true)
              }}
            >
              添加问题
            </Button>
          )
        }
        style={{ marginBottom: 16 }}
      >
        <Table
          columns={issueColumns}
          dataSource={qualityReport.issues}
          rowKey="id"
          pagination={false}
          size="small"
        />
      </Card>

      {/* Recommendations */}
      {qualityReport.recommendations.length > 0 && (
        <Card title="改进建议">
          <List
            dataSource={qualityReport.recommendations}
            renderItem={(recommendation, index) => (
              <List.Item>
                <List.Item.Meta
                  avatar={<Badge count={index + 1} style={{ backgroundColor: '#1890ff' }} />}
                  description={recommendation}
                />
              </List.Item>
            )}
          />
        </Card>
      )}

      {/* Issue Modal */}
      <Modal
        title={selectedIssue ? '更新问题' : '添加质量问题'}
        open={issueModalVisible}
        onCancel={() => setIssueModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form
          form={form}
          onFinish={selectedIssue ? handleIssueUpdate : handleIssueCreate}
          layout="vertical"
        >
          {!selectedIssue && (
            <>
              <Form.Item
                name="title"
                label="问题标题"
                rules={[{ required: true, message: '请输入问题标题' }]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="description"
                label="问题描述"
                rules={[{ required: true, message: '请输入问题描述' }]}
              >
                <TextArea rows={3} />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="severity"
                    label="严重程度"
                    rules={[{ required: true, message: '请选择严重程度' }]}
                  >
                    <Select>
                      <Option value="low">低</Option>
                      <Option value="medium">中</Option>
                      <Option value="high">高</Option>
                      <Option value="critical">严重</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="category"
                    label="问题分类"
                    rules={[{ required: true, message: '请选择问题分类' }]}
                  >
                    <Select>
                      <Option value="functionality">功能</Option>
                      <Option value="performance">性能</Option>
                      <Option value="usability">易用性</Option>
                      <Option value="security">安全</Option>
                      <Option value="compatibility">兼容性</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="assigneeName" label="负责人">
                    <Input />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="dueDate" label="截止日期">
                    <Input type="date" />
                  </Form.Item>
                </Col>
              </Row>
            </>
          )}

          {selectedIssue && (
            <>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select>
                  <Option value="open">待处理</Option>
                  <Option value="in_progress">处理中</Option>
                  <Option value="resolved">已解决</Option>
                  <Option value="closed">已关闭</Option>
                </Select>
              </Form.Item>

              <Form.Item name="resolution" label="解决方案">
                <TextArea rows={4} placeholder="请描述解决方案..." />
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>

      {/* Criteria Modal */}
      <Modal
        title="编辑评分"
        open={criteriaModalVisible}
        onCancel={() => setCriteriaModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} onFinish={handleCriteriaUpdate} layout="vertical">
          <Form.Item
            name="score"
            label="评分"
            rules={[{ required: true, message: '请输入评分' }]}
          >
            <Input type="number" min={0} max={100} addonAfter="分" />
          </Form.Item>

          <Form.Item name="comments" label="评价">
            <TextArea rows={4} placeholder="请输入评价内容..." />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default QualityControl
