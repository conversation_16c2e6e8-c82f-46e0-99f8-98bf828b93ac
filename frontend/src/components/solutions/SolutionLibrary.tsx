import React, { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Input,
  Select,
  Tag,
  Button,
  Space,
  Typography,
  Rate,
  Avatar,
  Tooltip,
  Badge,
  Modal,
  Tabs,
  List,
  Divider,
  Image,
} from 'antd'
import {
  SearchOutlined,
  FilterOutlined,
  EyeOutlined,
  CopyOutlined,
  StarOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  UserOutlined,
  TagOutlined,
  AppstoreOutlined,
  CodeOutlined,
} from '@ant-design/icons'
import { SolutionTemplate, SolutionType, ComponentType } from '../../types/solutions'

const { Text, Title, Paragraph } = Typography
const { Option } = Select
const { Search } = Input
const { TabPane } = Tabs

interface SolutionLibraryProps {
  templates: SolutionTemplate[]
  onSelectTemplate: (template: SolutionTemplate) => void
  onPreviewTemplate: (template: SolutionTemplate) => void
  onCloneTemplate: (template: SolutionTemplate) => void
  loading?: boolean
}

// Mock solution templates
const mockTemplates: SolutionTemplate[] = [
  {
    id: 'template_1',
    name: '企业管理系统',
    description: '完整的企业内部管理系统，包含人事、财务、项目管理等核心模块',
    type: SolutionType.SOFTWARE_DEVELOPMENT,
    category: '企业应用',
    industry: ['制造业', '服务业', '零售业'],
    tags: ['ERP', '管理系统', '企业应用'],
    components: ['comp_1', 'comp_2', 'comp_3'],
    estimatedDuration: 90,
    baseCost: 150000,
    complexity: 'high',
    popularity: 95,
    rating: 4.8,
    usageCount: 156,
    thumbnail: '',
    screenshots: [],
    features: ['用户管理', '权限控制', '数据分析', '报表生成'],
    benefits: ['提高效率', '降低成本', '数据统一'],
    requirements: ['Windows/Linux服务器', '数据库支持', '网络环境'],
    limitations: ['需要定制开发', '部署周期较长'],
    documentation: '详细的技术文档和用户手册',
    isPublic: true,
    createdBy: '1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
  },
  {
    id: 'template_2',
    name: '电商平台解决方案',
    description: '全功能电商平台，支持多商户、移动端、支付集成等功能',
    type: SolutionType.SOFTWARE_DEVELOPMENT,
    category: '电商平台',
    industry: ['零售业', '电商'],
    tags: ['电商', '多商户', '移动端'],
    components: ['comp_4', 'comp_5', 'comp_6'],
    estimatedDuration: 120,
    baseCost: 200000,
    complexity: 'high',
    popularity: 88,
    rating: 4.6,
    usageCount: 89,
    thumbnail: '',
    screenshots: [],
    features: ['商品管理', '订单处理', '支付集成', '物流跟踪'],
    benefits: ['快速上线', '功能完整', '可扩展性强'],
    requirements: ['云服务器', '支付接口', 'CDN支持'],
    limitations: ['需要支付接口申请', '运营成本较高'],
    documentation: '完整的开发和运营文档',
    isPublic: true,
    createdBy: '2',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-14T00:00:00Z',
  },
  {
    id: 'template_3',
    name: '数据分析平台',
    description: '企业级数据分析和可视化平台，支持多数据源集成',
    type: SolutionType.SYSTEM_INTEGRATION,
    category: '数据分析',
    industry: ['金融', '制造业', '互联网'],
    tags: ['大数据', '可视化', '分析'],
    components: ['comp_7', 'comp_8', 'comp_9'],
    estimatedDuration: 60,
    baseCost: 80000,
    complexity: 'medium',
    popularity: 76,
    rating: 4.4,
    usageCount: 67,
    thumbnail: '',
    screenshots: [],
    features: ['数据集成', '实时分析', '可视化图表', '报告生成'],
    benefits: ['数据驱动决策', '实时监控', '成本优化'],
    requirements: ['数据源接口', '计算资源', '存储空间'],
    limitations: ['数据质量要求高', '需要专业运维'],
    documentation: '数据分析和运维指南',
    isPublic: true,
    createdBy: '3',
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-13T00:00:00Z',
  },
]

const SolutionLibrary: React.FC<SolutionLibraryProps> = ({
  templates = mockTemplates,
  onSelectTemplate,
  onPreviewTemplate,
  onCloneTemplate,
  loading = false,
}) => {
  const [searchText, setSearchText] = useState('')
  const [typeFilter, setTypeFilter] = useState<SolutionType | 'all'>('all')
  const [complexityFilter, setComplexityFilter] = useState<string>('all')
  const [industryFilter, setIndustryFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('popularity')
  const [previewVisible, setPreviewVisible] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<SolutionTemplate | null>(null)

  // Filter and sort templates
  const filteredTemplates = templates
    .filter(template => {
      const matchesSearch = template.name.toLowerCase().includes(searchText.toLowerCase()) ||
                           template.description.toLowerCase().includes(searchText.toLowerCase()) ||
                           template.tags.some(tag => tag.toLowerCase().includes(searchText.toLowerCase()))
      
      const matchesType = typeFilter === 'all' || template.type === typeFilter
      const matchesComplexity = complexityFilter === 'all' || template.complexity === complexityFilter
      const matchesIndustry = industryFilter === 'all' || template.industry.includes(industryFilter)
      
      return matchesSearch && matchesType && matchesComplexity && matchesIndustry
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'popularity':
          return b.popularity - a.popularity
        case 'rating':
          return b.rating - a.rating
        case 'cost':
          return a.baseCost - b.baseCost
        case 'duration':
          return a.estimatedDuration - b.estimatedDuration
        case 'name':
          return a.name.localeCompare(b.name)
        default:
          return 0
      }
    })

  const handlePreview = (template: SolutionTemplate) => {
    setSelectedTemplate(template)
    setPreviewVisible(true)
  }

  const getComplexityColor = (complexity: string) => {
    const colors = {
      low: 'green',
      medium: 'orange',
      high: 'red',
    }
    return colors[complexity as keyof typeof colors]
  }

  const getTypeIcon = (type: SolutionType) => {
    const icons = {
      [SolutionType.SOFTWARE_DEVELOPMENT]: <CodeOutlined />,
      [SolutionType.SYSTEM_INTEGRATION]: <AppstoreOutlined />,
      [SolutionType.CONSULTING]: <UserOutlined />,
      [SolutionType.MAINTENANCE]: <TagOutlined />,
      [SolutionType.TRAINING]: <StarOutlined />,
      [SolutionType.CUSTOM]: <FilterOutlined />,
    }
    return icons[type]
  }

  const formatCurrency = (amount: number) => {
    return `¥${amount.toLocaleString()}`
  }

  return (
    <div>
      {/* Search and Filters */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索解决方案..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              prefix={<SearchOutlined />}
            />
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              value={typeFilter}
              onChange={setTypeFilter}
              style={{ width: '100%' }}
              placeholder="类型"
            >
              <Option value="all">全部类型</Option>
              <Option value={SolutionType.SOFTWARE_DEVELOPMENT}>软件开发</Option>
              <Option value={SolutionType.SYSTEM_INTEGRATION}>系统集成</Option>
              <Option value={SolutionType.CONSULTING}>咨询服务</Option>
              <Option value={SolutionType.MAINTENANCE}>维护服务</Option>
              <Option value={SolutionType.TRAINING}>培训服务</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              value={complexityFilter}
              onChange={setComplexityFilter}
              style={{ width: '100%' }}
              placeholder="复杂度"
            >
              <Option value="all">全部复杂度</Option>
              <Option value="low">简单</Option>
              <Option value="medium">中等</Option>
              <Option value="high">复杂</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              value={industryFilter}
              onChange={setIndustryFilter}
              style={{ width: '100%' }}
              placeholder="行业"
            >
              <Option value="all">全部行业</Option>
              <Option value="制造业">制造业</Option>
              <Option value="服务业">服务业</Option>
              <Option value="零售业">零售业</Option>
              <Option value="金融">金融</Option>
              <Option value="互联网">互联网</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              value={sortBy}
              onChange={setSortBy}
              style={{ width: '100%' }}
              placeholder="排序"
            >
              <Option value="popularity">热门度</Option>
              <Option value="rating">评分</Option>
              <Option value="cost">成本</Option>
              <Option value="duration">工期</Option>
              <Option value="name">名称</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* Templates Grid */}
      <Row gutter={[16, 16]}>
        {filteredTemplates.map(template => (
          <Col xs={24} sm={12} lg={8} xl={6} key={template.id}>
            <Card
              hoverable
              cover={
                template.thumbnail ? (
                  <Image
                    alt={template.name}
                    src={template.thumbnail}
                    height={160}
                    style={{ objectFit: 'cover' }}
                  />
                ) : (
                  <div
                    style={{
                      height: 160,
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: 48,
                      color: 'white',
                    }}
                  >
                    {getTypeIcon(template.type)}
                  </div>
                )
              }
              actions={[
                <Tooltip title="预览">
                  <Button
                    type="text"
                    icon={<EyeOutlined />}
                    onClick={() => handlePreview(template)}
                  />
                </Tooltip>,
                <Tooltip title="使用模板">
                  <Button
                    type="text"
                    icon={<CopyOutlined />}
                    onClick={() => onSelectTemplate(template)}
                  />
                </Tooltip>,
              ]}
            >
              <Card.Meta
                title={
                  <div>
                    <Text strong>{template.name}</Text>
                    <div style={{ marginTop: 4 }}>
                      <Rate
                        disabled
                        defaultValue={template.rating}
                        style={{ fontSize: 12 }}
                      />
                      <Text type="secondary" style={{ marginLeft: 8, fontSize: 12 }}>
                        ({template.usageCount})
                      </Text>
                    </div>
                  </div>
                }
                description={
                  <div>
                    <Paragraph
                      ellipsis={{ rows: 2 }}
                      style={{ marginBottom: 8, fontSize: 12 }}
                    >
                      {template.description}
                    </Paragraph>
                    
                    <Space wrap style={{ marginBottom: 8 }}>
                      <Tag color={getComplexityColor(template.complexity)} size="small">
                        {template.complexity === 'low' ? '简单' :
                         template.complexity === 'medium' ? '中等' : '复杂'}
                      </Tag>
                      <Tag icon={<ClockCircleOutlined />} size="small">
                        {template.estimatedDuration}天
                      </Tag>
                      <Tag icon={<DollarOutlined />} size="small">
                        {formatCurrency(template.baseCost)}
                      </Tag>
                    </Space>

                    <div>
                      {template.tags.slice(0, 2).map(tag => (
                        <Tag key={tag} size="small" style={{ fontSize: 10 }}>
                          {tag}
                        </Tag>
                      ))}
                      {template.tags.length > 2 && (
                        <Tag size="small" style={{ fontSize: 10 }}>
                          +{template.tags.length - 2}
                        </Tag>
                      )}
                    </div>
                  </div>
                }
              />
            </Card>
          </Col>
        ))}
      </Row>

      {filteredTemplates.length === 0 && (
        <Card>
          <div style={{ textAlign: 'center', padding: 40 }}>
            <Text type="secondary">没有找到匹配的解决方案模板</Text>
          </div>
        </Card>
      )}

      {/* Preview Modal */}
      <Modal
        title={selectedTemplate?.name}
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width="80%"
        footer={[
          <Button key="cancel" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>,
          <Button
            key="clone"
            onClick={() => {
              if (selectedTemplate) {
                onCloneTemplate(selectedTemplate)
                setPreviewVisible(false)
              }
            }}
          >
            克隆模板
          </Button>,
          <Button
            key="use"
            type="primary"
            onClick={() => {
              if (selectedTemplate) {
                onSelectTemplate(selectedTemplate)
                setPreviewVisible(false)
              }
            }}
          >
            使用模板
          </Button>,
        ]}
      >
        {selectedTemplate && (
          <Tabs defaultActiveKey="overview">
            <TabPane tab="概览" key="overview">
              <Row gutter={16}>
                <Col span={16}>
                  <Paragraph>{selectedTemplate.description}</Paragraph>
                  
                  <Title level={5}>主要功能</Title>
                  <List
                    size="small"
                    dataSource={selectedTemplate.features}
                    renderItem={item => <List.Item>• {item}</List.Item>}
                  />

                  <Title level={5}>业务价值</Title>
                  <List
                    size="small"
                    dataSource={selectedTemplate.benefits}
                    renderItem={item => <List.Item>• {item}</List.Item>}
                  />
                </Col>
                <Col span={8}>
                  <Card size="small">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text type="secondary">评分</Text>
                        <div>
                          <Rate disabled defaultValue={selectedTemplate.rating} />
                          <Text style={{ marginLeft: 8 }}>
                            {selectedTemplate.rating}/5
                          </Text>
                        </div>
                      </div>
                      
                      <div>
                        <Text type="secondary">使用次数</Text>
                        <div>{selectedTemplate.usageCount}</div>
                      </div>
                      
                      <div>
                        <Text type="secondary">预估工期</Text>
                        <div>{selectedTemplate.estimatedDuration} 天</div>
                      </div>
                      
                      <div>
                        <Text type="secondary">基础成本</Text>
                        <div>{formatCurrency(selectedTemplate.baseCost)}</div>
                      </div>
                      
                      <div>
                        <Text type="secondary">复杂度</Text>
                        <div>
                          <Tag color={getComplexityColor(selectedTemplate.complexity)}>
                            {selectedTemplate.complexity === 'low' ? '简单' :
                             selectedTemplate.complexity === 'medium' ? '中等' : '复杂'}
                          </Tag>
                        </div>
                      </div>
                    </Space>
                  </Card>
                </Col>
              </Row>
            </TabPane>
            
            <TabPane tab="技术要求" key="requirements">
              <Title level={5}>系统要求</Title>
              <List
                dataSource={selectedTemplate.requirements}
                renderItem={item => <List.Item>• {item}</List.Item>}
              />
              
              <Title level={5}>限制条件</Title>
              <List
                dataSource={selectedTemplate.limitations}
                renderItem={item => <List.Item>• {item}</List.Item>}
              />
            </TabPane>
            
            <TabPane tab="标签" key="tags">
              <Space wrap>
                {selectedTemplate.tags.map(tag => (
                  <Tag key={tag}>{tag}</Tag>
                ))}
              </Space>
              
              <Divider />
              
              <Space wrap>
                {selectedTemplate.industry.map(industry => (
                  <Tag key={industry} color="blue">{industry}</Tag>
                ))}
              </Space>
            </TabPane>
          </Tabs>
        )}
      </Modal>
    </div>
  )
}

export default SolutionLibrary
