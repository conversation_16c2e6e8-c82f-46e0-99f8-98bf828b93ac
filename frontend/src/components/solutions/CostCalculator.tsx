import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Row,
  Col,
  Statistic,
  Progress,
  Typography,
  Tag,
  Button,
  Space,
  InputNumber,
  Select,
  Tooltip,
  Alert,
  Divider,
  Collapse,
  List,
} from 'antd'
import {
  DollarOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  CalculatorOutlined,
  FileExcelOutlined,
  PrinterOutlined,
} from '@ant-design/icons'
import { SolutionComponent, CostBreakdown, ComponentCost, TimelineEstimate } from '../../types/solutions'
import type { ColumnsType } from 'antd/es/table'

const { Text, Title } = Typography
const { Panel } = Collapse
const { Option } = Select

interface CostCalculatorProps {
  components: SolutionComponent[]
  onCostChange: (breakdown: CostBreakdown) => void
  onTimelineChange: (timeline: TimelineEstimate) => void
  currency?: string
  taxRate?: number
  discountRate?: number
}

const CostCalculator: React.FC<CostCalculatorProps> = ({
  components,
  onCostChange,
  onTimelineChange,
  currency = 'CNY',
  taxRate = 0.13,
  discountRate = 0,
}) => {
  const [costBreakdown, setCostBreakdown] = useState<CostBreakdown>({
    components: [],
    customization: 0,
    integration: 0,
    testing: 0,
    deployment: 0,
    training: 0,
    maintenance: 0,
    contingency: 0,
    total: 0,
    currency,
  })

  const [timelineEstimate, setTimelineEstimate] = useState<TimelineEstimate>({
    phases: [],
    totalDuration: 0,
    milestones: [],
  })

  const [customizationRate, setCustomizationRate] = useState(0.2)
  const [integrationRate, setIntegrationRate] = useState(0.15)
  const [testingRate, setTestingRate] = useState(0.1)
  const [deploymentRate, setDeploymentRate] = useState(0.05)
  const [trainingRate, setTrainingRate] = useState(0.08)
  const [maintenanceRate, setMaintenanceRate] = useState(0.12)
  const [contingencyRate, setContingencyRate] = useState(0.1)

  useEffect(() => {
    calculateCosts()
  }, [
    components,
    customizationRate,
    integrationRate,
    testingRate,
    deploymentRate,
    trainingRate,
    maintenanceRate,
    contingencyRate,
  ])

  const calculateCosts = () => {
    const componentCosts: ComponentCost[] = components.map(component => {
      const baseCost = component.baseCost
      const customizationCost = baseCost * customizationRate
      const integrationCost = baseCost * integrationRate
      
      return {
        componentId: component.id,
        componentName: component.name,
        baseCost,
        customizationCost,
        integrationCost,
        quantity: 1,
        total: baseCost + customizationCost + integrationCost,
      }
    })

    const subtotal = componentCosts.reduce((sum, cost) => sum + cost.baseCost, 0)
    const customization = subtotal * customizationRate
    const integration = subtotal * integrationRate
    const testing = subtotal * testingRate
    const deployment = subtotal * deploymentRate
    const training = subtotal * trainingRate
    const maintenance = subtotal * maintenanceRate
    const contingency = (subtotal + customization + integration + testing + deployment + training) * contingencyRate
    
    const total = subtotal + customization + integration + testing + deployment + training + maintenance + contingency

    const breakdown: CostBreakdown = {
      components: componentCosts,
      customization,
      integration,
      testing,
      deployment,
      training,
      maintenance,
      contingency,
      total,
      currency,
    }

    setCostBreakdown(breakdown)
    onCostChange(breakdown)

    // Calculate timeline
    const totalHours = components.reduce((sum, comp) => sum + comp.estimatedHours, 0)
    const totalDuration = Math.ceil(totalHours / 8) // Convert to days

    const timeline: TimelineEstimate = {
      phases: [
        {
          id: 'design',
          name: '设计阶段',
          description: '需求分析和系统设计',
          duration: Math.ceil(totalDuration * 0.2),
          dependencies: [],
          components: components.map(c => c.id),
          resources: [
            { role: '架构师', skillLevel: 'senior', hoursRequired: totalHours * 0.1, costPerHour: 800 },
            { role: '设计师', skillLevel: 'mid', hoursRequired: totalHours * 0.1, costPerHour: 600 },
          ],
          deliverables: ['系统架构图', '技术方案', 'UI设计稿'],
        },
        {
          id: 'development',
          name: '开发阶段',
          description: '核心功能开发',
          duration: Math.ceil(totalDuration * 0.5),
          dependencies: ['design'],
          components: components.map(c => c.id),
          resources: [
            { role: '前端工程师', skillLevel: 'mid', hoursRequired: totalHours * 0.3, costPerHour: 700 },
            { role: '后端工程师', skillLevel: 'senior', hoursRequired: totalHours * 0.4, costPerHour: 800 },
          ],
          deliverables: ['功能模块', '接口文档', '单元测试'],
        },
        {
          id: 'testing',
          name: '测试阶段',
          description: '系统测试和优化',
          duration: Math.ceil(totalDuration * 0.2),
          dependencies: ['development'],
          components: components.map(c => c.id),
          resources: [
            { role: '测试工程师', skillLevel: 'mid', hoursRequired: totalHours * 0.15, costPerHour: 600 },
          ],
          deliverables: ['测试报告', '性能报告', '安全评估'],
        },
        {
          id: 'deployment',
          name: '部署阶段',
          description: '系统部署和上线',
          duration: Math.ceil(totalDuration * 0.1),
          dependencies: ['testing'],
          components: components.map(c => c.id),
          resources: [
            { role: '运维工程师', skillLevel: 'senior', hoursRequired: totalHours * 0.05, costPerHour: 750 },
          ],
          deliverables: ['部署文档', '运维手册', '监控配置'],
        },
      ],
      totalDuration,
      milestones: [
        {
          id: 'milestone_1',
          name: '设计完成',
          description: '系统设计和架构确认',
          dueDate: new Date(Date.now() + totalDuration * 0.2 * 24 * 60 * 60 * 1000).toISOString(),
          dependencies: ['design'],
          deliverables: ['设计文档'],
          criteria: ['设计评审通过', '客户确认'],
        },
        {
          id: 'milestone_2',
          name: '开发完成',
          description: '核心功能开发完成',
          dueDate: new Date(Date.now() + totalDuration * 0.7 * 24 * 60 * 60 * 1000).toISOString(),
          dependencies: ['development'],
          deliverables: ['功能演示'],
          criteria: ['功能测试通过', '代码审查完成'],
        },
        {
          id: 'milestone_3',
          name: '项目交付',
          description: '系统正式上线',
          dueDate: new Date(Date.now() + totalDuration * 24 * 60 * 60 * 1000).toISOString(),
          dependencies: ['deployment'],
          deliverables: ['完整系统'],
          criteria: ['用户验收通过', '文档交付完成'],
        },
      ],
    }

    setTimelineEstimate(timeline)
    onTimelineChange(timeline)
  }

  const formatCurrency = (amount: number) => {
    return `¥${amount.toLocaleString()}`
  }

  const componentColumns: ColumnsType<ComponentCost> = [
    {
      title: '组件名称',
      dataIndex: 'componentName',
      key: 'componentName',
    },
    {
      title: '基础成本',
      dataIndex: 'baseCost',
      key: 'baseCost',
      render: (value) => formatCurrency(value),
      align: 'right',
    },
    {
      title: '定制成本',
      dataIndex: 'customizationCost',
      key: 'customizationCost',
      render: (value) => formatCurrency(value),
      align: 'right',
    },
    {
      title: '集成成本',
      dataIndex: 'integrationCost',
      key: 'integrationCost',
      render: (value) => formatCurrency(value),
      align: 'right',
    },
    {
      title: '小计',
      dataIndex: 'total',
      key: 'total',
      render: (value) => <Text strong>{formatCurrency(value)}</Text>,
      align: 'right',
    },
  ]

  const getCostColor = (percentage: number) => {
    if (percentage < 0.1) return '#52c41a'
    if (percentage < 0.2) return '#faad14'
    return '#ff4d4f'
  }

  return (
    <div>
      {/* Cost Overview */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总成本"
              value={costBreakdown.total}
              formatter={(value) => formatCurrency(Number(value))}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="预计工期"
              value={timelineEstimate.totalDuration}
              suffix="天"
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="组件数量"
              value={components.length}
              suffix="个"
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="平均复杂度"
              value={components.length > 0 ? 
                (components.filter(c => c.complexity === 'high').length * 3 +
                 components.filter(c => c.complexity === 'medium').length * 2 +
                 components.filter(c => c.complexity === 'low').length * 1) / components.length : 0}
              precision={1}
              suffix="/3"
              prefix={<CalculatorOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Cost Breakdown */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="成本明细" extra={
            <Space>
              <Button icon={<FileExcelOutlined />} size="small">
                导出Excel
              </Button>
              <Button icon={<PrinterOutlined />} size="small">
                打印报告
              </Button>
            </Space>
          }>
            <Table
              columns={componentColumns}
              dataSource={costBreakdown.components}
              rowKey="componentId"
              pagination={false}
              size="small"
              summary={() => (
                <Table.Summary>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0}>
                      <Text strong>组件小计</Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={1} align="right">
                      <Text strong>
                        {formatCurrency(costBreakdown.components.reduce((sum, item) => sum + item.baseCost, 0))}
                      </Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={2} align="right">
                      <Text strong>
                        {formatCurrency(costBreakdown.components.reduce((sum, item) => sum + item.customizationCost, 0))}
                      </Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3} align="right">
                      <Text strong>
                        {formatCurrency(costBreakdown.components.reduce((sum, item) => sum + item.integrationCost, 0))}
                      </Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={4} align="right">
                      <Text strong>
                        {formatCurrency(costBreakdown.components.reduce((sum, item) => sum + item.total, 0))}
                      </Text>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              )}
            />

            <Divider />

            <Row gutter={[16, 8]}>
              <Col span={12}>
                <Text>定制开发 ({(customizationRate * 100).toFixed(0)}%)</Text>
              </Col>
              <Col span={12} style={{ textAlign: 'right' }}>
                <Text>{formatCurrency(costBreakdown.customization)}</Text>
              </Col>

              <Col span={12}>
                <Text>系统集成 ({(integrationRate * 100).toFixed(0)}%)</Text>
              </Col>
              <Col span={12} style={{ textAlign: 'right' }}>
                <Text>{formatCurrency(costBreakdown.integration)}</Text>
              </Col>

              <Col span={12}>
                <Text>测试验证 ({(testingRate * 100).toFixed(0)}%)</Text>
              </Col>
              <Col span={12} style={{ textAlign: 'right' }}>
                <Text>{formatCurrency(costBreakdown.testing)}</Text>
              </Col>

              <Col span={12}>
                <Text>部署上线 ({(deploymentRate * 100).toFixed(0)}%)</Text>
              </Col>
              <Col span={12} style={{ textAlign: 'right' }}>
                <Text>{formatCurrency(costBreakdown.deployment)}</Text>
              </Col>

              <Col span={12}>
                <Text>培训服务 ({(trainingRate * 100).toFixed(0)}%)</Text>
              </Col>
              <Col span={12} style={{ textAlign: 'right' }}>
                <Text>{formatCurrency(costBreakdown.training)}</Text>
              </Col>

              <Col span={12}>
                <Text>维护支持 ({(maintenanceRate * 100).toFixed(0)}%)</Text>
              </Col>
              <Col span={12} style={{ textAlign: 'right' }}>
                <Text>{formatCurrency(costBreakdown.maintenance)}</Text>
              </Col>

              <Col span={12}>
                <Text>风险预留 ({(contingencyRate * 100).toFixed(0)}%)</Text>
              </Col>
              <Col span={12} style={{ textAlign: 'right' }}>
                <Text>{formatCurrency(costBreakdown.contingency)}</Text>
              </Col>

              <Col span={24}>
                <Divider style={{ margin: '8px 0' }} />
              </Col>

              <Col span={12}>
                <Text strong style={{ fontSize: 16 }}>总计</Text>
              </Col>
              <Col span={12} style={{ textAlign: 'right' }}>
                <Text strong style={{ fontSize: 16, color: '#1890ff' }}>
                  {formatCurrency(costBreakdown.total)}
                </Text>
              </Col>
            </Row>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Space direction="vertical" style={{ width: '100%' }}>
            {/* Cost Distribution */}
            <Card title="成本分布" size="small">
              <div style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                  <Text>组件成本</Text>
                  <Text>{((costBreakdown.components.reduce((sum, item) => sum + item.total, 0) / costBreakdown.total) * 100).toFixed(1)}%</Text>
                </div>
                <Progress
                  percent={(costBreakdown.components.reduce((sum, item) => sum + item.total, 0) / costBreakdown.total) * 100}
                  strokeColor="#1890ff"
                  showInfo={false}
                />
              </div>

              <div style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                  <Text>服务成本</Text>
                  <Text>{(((costBreakdown.customization + costBreakdown.integration + costBreakdown.testing + costBreakdown.deployment + costBreakdown.training) / costBreakdown.total) * 100).toFixed(1)}%</Text>
                </div>
                <Progress
                  percent={((costBreakdown.customization + costBreakdown.integration + costBreakdown.testing + costBreakdown.deployment + costBreakdown.training) / costBreakdown.total) * 100}
                  strokeColor="#52c41a"
                  showInfo={false}
                />
              </div>

              <div style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                  <Text>维护成本</Text>
                  <Text>{((costBreakdown.maintenance / costBreakdown.total) * 100).toFixed(1)}%</Text>
                </div>
                <Progress
                  percent={(costBreakdown.maintenance / costBreakdown.total) * 100}
                  strokeColor="#faad14"
                  showInfo={false}
                />
              </div>

              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                  <Text>风险预留</Text>
                  <Text>{((costBreakdown.contingency / costBreakdown.total) * 100).toFixed(1)}%</Text>
                </div>
                <Progress
                  percent={(costBreakdown.contingency / costBreakdown.total) * 100}
                  strokeColor="#ff4d4f"
                  showInfo={false}
                />
              </div>
            </Card>

            {/* Cost Adjustment */}
            <Card title="成本调整" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text>定制开发比例</Text>
                  <InputNumber
                    value={customizationRate * 100}
                    onChange={(value) => setCustomizationRate((value || 0) / 100)}
                    min={0}
                    max={100}
                    formatter={value => `${value}%`}
                    parser={value => value!.replace('%', '')}
                    style={{ width: '100%', marginTop: 4 }}
                  />
                </div>

                <div>
                  <Text>集成服务比例</Text>
                  <InputNumber
                    value={integrationRate * 100}
                    onChange={(value) => setIntegrationRate((value || 0) / 100)}
                    min={0}
                    max={100}
                    formatter={value => `${value}%`}
                    parser={value => value!.replace('%', '')}
                    style={{ width: '100%', marginTop: 4 }}
                  />
                </div>

                <div>
                  <Text>风险预留比例</Text>
                  <InputNumber
                    value={contingencyRate * 100}
                    onChange={(value) => setContingencyRate((value || 0) / 100)}
                    min={0}
                    max={50}
                    formatter={value => `${value}%`}
                    parser={value => value!.replace('%', '')}
                    style={{ width: '100%', marginTop: 4 }}
                  />
                </div>
              </Space>
            </Card>

            {/* Warnings */}
            {costBreakdown.total > 500000 && (
              <Alert
                message="高成本警告"
                description="项目总成本超过50万，建议进行成本优化。"
                type="warning"
                icon={<WarningOutlined />}
                showIcon
              />
            )}

            {timelineEstimate.totalDuration > 180 && (
              <Alert
                message="长周期提醒"
                description="项目周期超过6个月，建议分阶段实施。"
                type="info"
                icon={<InfoCircleOutlined />}
                showIcon
              />
            )}
          </Space>
        </Col>
      </Row>

      {/* Timeline */}
      <Card title="项目时间线" style={{ marginTop: 16 }}>
        <Collapse>
          {timelineEstimate.phases.map((phase, index) => (
            <Panel
              header={
                <Space>
                  <Text strong>{phase.name}</Text>
                  <Tag>{phase.duration}天</Tag>
                  <Text type="secondary">{phase.description}</Text>
                </Space>
              }
              key={phase.id}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Title level={5}>交付物</Title>
                  <List
                    size="small"
                    dataSource={phase.deliverables}
                    renderItem={item => <List.Item>• {item}</List.Item>}
                  />
                </Col>
                <Col span={12}>
                  <Title level={5}>资源需求</Title>
                  <List
                    size="small"
                    dataSource={phase.resources}
                    renderItem={resource => (
                      <List.Item>
                        • {resource.role} ({resource.skillLevel}) - {resource.hoursRequired}h @ {formatCurrency(resource.costPerHour)}/h
                      </List.Item>
                    )}
                  />
                </Col>
              </Row>
            </Panel>
          ))}
        </Collapse>
      </Card>
    </div>
  )
}

export default CostCalculator
