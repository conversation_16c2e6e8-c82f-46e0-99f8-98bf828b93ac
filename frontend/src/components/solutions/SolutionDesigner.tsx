import React, { useState, useRef, useCallback } from 'react'
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Typography,
  Drawer,
  List,
  Tag,
  Input,
  Tooltip,
  Modal,
  Form,
  InputNumber,
  Select,
  message,
  Divider,
  Collapse,
} from 'antd'
import {
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined,
  SettingOutlined,
  AppstoreOutlined,
  LinkOutlined,
} from '@ant-design/icons'
import { SolutionComponent, ComponentType, DesignerState } from '../../types/solutions'

const { Text, Title } = Typography
const { Panel } = Collapse
const { Option } = Select

interface SolutionDesignerProps {
  initialComponents?: SolutionComponent[]
  onSave: (components: SolutionComponent[], connections: any[]) => void
  onValidate: (components: SolutionComponent[]) => void
  readonly?: boolean
}

// Mock component library
const mockComponentLibrary: SolutionComponent[] = [
  {
    id: 'comp_frontend_1',
    name: 'React前端框架',
    description: '基于React的现代化前端框架',
    type: ComponentType.FRONTEND,
    category: '前端框架',
    version: '18.0.0',
    isRequired: false,
    dependencies: [],
    conflictsWith: ['comp_frontend_2'],
    estimatedHours: 40,
    baseCost: 15000,
    complexity: 'medium',
    technologies: ['React', 'TypeScript', 'Ant Design'],
    features: ['组件化开发', '状态管理', '路由管理'],
    documentation: 'React开发文档',
    icon: '⚛️',
    color: '#61dafb',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'comp_backend_1',
    name: 'Node.js后端服务',
    description: '基于Node.js的RESTful API服务',
    type: ComponentType.BACKEND,
    category: '后端服务',
    version: '18.0.0',
    isRequired: true,
    dependencies: ['comp_database_1'],
    conflictsWith: [],
    estimatedHours: 80,
    baseCost: 25000,
    complexity: 'high',
    technologies: ['Node.js', 'Express', 'TypeScript'],
    features: ['RESTful API', '身份认证', '数据验证'],
    documentation: 'Node.js开发文档',
    icon: '🟢',
    color: '#339933',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'comp_database_1',
    name: 'PostgreSQL数据库',
    description: '企业级关系型数据库',
    type: ComponentType.DATABASE,
    category: '数据库',
    version: '15.0',
    isRequired: true,
    dependencies: [],
    conflictsWith: ['comp_database_2'],
    estimatedHours: 20,
    baseCost: 8000,
    complexity: 'medium',
    technologies: ['PostgreSQL', 'SQL'],
    features: ['ACID事务', '数据完整性', '高性能'],
    documentation: 'PostgreSQL使用文档',
    icon: '🐘',
    color: '#336791',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'comp_api_1',
    name: 'GraphQL API',
    description: '灵活的API查询语言',
    type: ComponentType.API,
    category: 'API服务',
    version: '16.0.0',
    isRequired: false,
    dependencies: ['comp_backend_1'],
    conflictsWith: [],
    estimatedHours: 30,
    baseCost: 12000,
    complexity: 'medium',
    technologies: ['GraphQL', 'Apollo'],
    features: ['类型安全', '查询优化', '实时订阅'],
    documentation: 'GraphQL开发指南',
    icon: '🔗',
    color: '#e10098',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
]

const SolutionDesigner: React.FC<SolutionDesignerProps> = ({
  initialComponents = [],
  onSave,
  onValidate,
  readonly = false,
}) => {
  const [designerState, setDesignerState] = useState<DesignerState>({
    canvas: {
      id: 'main-canvas',
      width: 1200,
      height: 800,
      zoom: 1,
      offset: { x: 0, y: 0 },
      grid: { enabled: true, size: 20, color: '#f0f0f0' },
      background: '#fafafa',
    },
    components: initialComponents,
    connections: [],
    selectedItems: [],
    clipboard: [],
    history: [],
    historyIndex: -1,
  })

  const [componentLibraryVisible, setComponentLibraryVisible] = useState(false)
  const [configModalVisible, setConfigModalVisible] = useState(false)
  const [selectedComponent, setSelectedComponent] = useState<SolutionComponent | null>(null)
  const [draggedComponent, setDraggedComponent] = useState<SolutionComponent | null>(null)
  const canvasRef = useRef<HTMLDivElement>(null)

  const handleDragStart = (component: SolutionComponent) => {
    setDraggedComponent(component)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    if (!draggedComponent || readonly) return

    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return

    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    const newComponent: SolutionComponent = {
      ...draggedComponent,
      id: `${draggedComponent.id}_${Date.now()}`,
      position: { x, y },
      size: { width: 120, height: 80 },
    }

    setDesignerState(prev => ({
      ...prev,
      components: [...prev.components, newComponent],
    }))

    setDraggedComponent(null)
    message.success(`已添加组件: ${newComponent.name}`)
  }

  const handleComponentClick = (component: SolutionComponent) => {
    if (readonly) return
    setSelectedComponent(component)
    setConfigModalVisible(true)
  }

  const handleDeleteComponent = (componentId: string) => {
    if (readonly) return
    setDesignerState(prev => ({
      ...prev,
      components: prev.components.filter(c => c.id !== componentId),
      connections: prev.connections.filter(
        conn => conn.sourceId !== componentId && conn.targetId !== componentId
      ),
    }))
    message.success('组件已删除')
  }

  const handleSave = () => {
    onSave(designerState.components, designerState.connections)
    message.success('解决方案已保存')
  }

  const handleValidate = () => {
    onValidate(designerState.components)
  }

  const getComponentTypeColor = (type: ComponentType) => {
    const colors = {
      [ComponentType.FRONTEND]: '#61dafb',
      [ComponentType.BACKEND]: '#339933',
      [ComponentType.DATABASE]: '#336791',
      [ComponentType.API]: '#e10098',
      [ComponentType.INTEGRATION]: '#ff6b35',
      [ComponentType.INFRASTRUCTURE]: '#4caf50',
      [ComponentType.SECURITY]: '#f44336',
      [ComponentType.MONITORING]: '#9c27b0',
      [ComponentType.TESTING]: '#ff9800',
      [ComponentType.DEPLOYMENT]: '#607d8b',
    }
    return colors[type] || '#666666'
  }

  const renderComponent = (component: SolutionComponent) => (
    <div
      key={component.id}
      style={{
        position: 'absolute',
        left: component.position?.x || 0,
        top: component.position?.y || 0,
        width: component.size?.width || 120,
        height: component.size?.height || 80,
        backgroundColor: 'white',
        border: `2px solid ${getComponentTypeColor(component.type)}`,
        borderRadius: 8,
        padding: 8,
        cursor: readonly ? 'default' : 'pointer',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
      }}
      onClick={() => handleComponentClick(component)}
    >
      <div style={{ fontSize: 20, marginBottom: 4 }}>
        {component.icon || '📦'}
      </div>
      <Text style={{ fontSize: 12, fontWeight: 'bold' }}>
        {component.name}
      </Text>
      <Text style={{ fontSize: 10, color: '#666' }}>
        {component.type}
      </Text>
      {!readonly && (
        <Button
          type="text"
          size="small"
          icon={<DeleteOutlined />}
          style={{
            position: 'absolute',
            top: -8,
            right: -8,
            backgroundColor: '#ff4d4f',
            color: 'white',
            borderRadius: '50%',
            width: 20,
            height: 20,
            minWidth: 20,
            padding: 0,
          }}
          onClick={(e) => {
            e.stopPropagation()
            handleDeleteComponent(component.id)
          }}
        />
      )}
    </div>
  )

  const groupedComponents = mockComponentLibrary.reduce((acc, component) => {
    if (!acc[component.type]) {
      acc[component.type] = []
    }
    acc[component.type].push(component)
    return acc
  }, {} as Record<ComponentType, SolutionComponent[]>)

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Toolbar */}
      <Card size="small" style={{ marginBottom: 0, borderRadius: 0 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<AppstoreOutlined />}
                onClick={() => setComponentLibraryVisible(true)}
                disabled={readonly}
              >
                组件库
              </Button>
              <Button icon={<UndoOutlined />} disabled>
                撤销
              </Button>
              <Button icon={<RedoOutlined />} disabled>
                重做
              </Button>
              <Divider type="vertical" />
              <Button icon={<ZoomInOutlined />}>
                放大
              </Button>
              <Button icon={<ZoomOutOutlined />}>
                缩小
              </Button>
              <Button icon={<FullscreenOutlined />}>
                全屏
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button onClick={handleValidate}>
                验证
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSave}
                disabled={readonly}
              >
                保存
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Canvas */}
      <div style={{ flex: 1, position: 'relative', overflow: 'hidden' }}>
        <div
          ref={canvasRef}
          style={{
            width: '100%',
            height: '100%',
            backgroundColor: designerState.canvas.background,
            backgroundImage: designerState.canvas.grid.enabled
              ? `radial-gradient(circle, ${designerState.canvas.grid.color} 1px, transparent 1px)`
              : 'none',
            backgroundSize: `${designerState.canvas.grid.size}px ${designerState.canvas.grid.size}px`,
            position: 'relative',
          }}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          {designerState.components.map(renderComponent)}
        </div>
      </div>

      {/* Component Library Drawer */}
      <Drawer
        title="组件库"
        placement="left"
        width={400}
        open={componentLibraryVisible}
        onClose={() => setComponentLibraryVisible(false)}
      >
        <Collapse defaultActiveKey={Object.keys(groupedComponents)}>
          {Object.entries(groupedComponents).map(([type, components]) => (
            <Panel
              header={
                <Space>
                  <Tag color={getComponentTypeColor(type as ComponentType)}>
                    {type}
                  </Tag>
                  <Text>({components.length})</Text>
                </Space>
              }
              key={type}
            >
              <List
                dataSource={components}
                renderItem={(component) => (
                  <List.Item
                    style={{
                      cursor: 'grab',
                      padding: 8,
                      border: '1px solid #f0f0f0',
                      borderRadius: 4,
                      marginBottom: 8,
                    }}
                    draggable
                    onDragStart={() => handleDragStart(component)}
                  >
                    <List.Item.Meta
                      avatar={
                        <div
                          style={{
                            width: 40,
                            height: 40,
                            backgroundColor: component.color,
                            borderRadius: 4,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: 20,
                          }}
                        >
                          {component.icon || '📦'}
                        </div>
                      }
                      title={component.name}
                      description={
                        <div>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            {component.description}
                          </Text>
                          <div style={{ marginTop: 4 }}>
                            <Tag size="small">
                              {component.estimatedHours}h
                            </Tag>
                            <Tag size="small">
                              ¥{component.baseCost.toLocaleString()}
                            </Tag>
                          </div>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Panel>
          ))}
        </Collapse>
      </Drawer>

      {/* Component Configuration Modal */}
      <Modal
        title={`配置组件: ${selectedComponent?.name}`}
        open={configModalVisible}
        onCancel={() => setConfigModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setConfigModalVisible(false)}>
            取消
          </Button>,
          <Button key="save" type="primary">
            保存配置
          </Button>,
        ]}
      >
        {selectedComponent && (
          <Form layout="vertical">
            <Form.Item label="组件名称">
              <Input defaultValue={selectedComponent.name} />
            </Form.Item>
            <Form.Item label="描述">
              <Input.TextArea defaultValue={selectedComponent.description} rows={3} />
            </Form.Item>
            <Form.Item label="预估工时">
              <InputNumber
                defaultValue={selectedComponent.estimatedHours}
                min={1}
                max={1000}
                addonAfter="小时"
              />
            </Form.Item>
            <Form.Item label="基础成本">
              <InputNumber
                defaultValue={selectedComponent.baseCost}
                min={0}
                formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value!.replace(/¥\s?|(,*)/g, '')}
              />
            </Form.Item>
            <Form.Item label="复杂度">
              <Select defaultValue={selectedComponent.complexity}>
                <Option value="low">简单</Option>
                <Option value="medium">中等</Option>
                <Option value="high">复杂</Option>
              </Select>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  )
}

export default SolutionDesigner
