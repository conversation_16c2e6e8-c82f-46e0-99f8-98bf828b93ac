import React, { useState } from 'react'
import {
  Card,
  Timeline,
  Progress,
  Button,
  Space,
  Typography,
  Tag,
  Modal,
  Form,
  Input,
  DatePicker,
  Select,
  List,
  Avatar,
  Tooltip,
  Alert,
  Statistic,
  Row,
  Col,
  Badge,
} from 'antd'
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  CalendarOutlined,
  TrophyOutlined,
  WarningOutlined,
  PlusOutlined,
  EditOutlined,
  FlagOutlined,
} from '@ant-design/icons'
import { Milestone, MilestoneStatus, Project } from '../../types/projects'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

interface MilestoneTrackerProps {
  projectId: string
  milestones: Milestone[]
  onMilestoneUpdate: (milestoneId: string, updates: Partial<Milestone>) => void
  onMilestoneCreate: (milestone: Omit<Milestone, 'id'>) => void
  onMilestoneComplete: (milestoneId: string) => void
  readonly?: boolean
}

// Mock milestones data
const mockMilestones: Milestone[] = [
  {
    id: 'milestone_1',
    projectId: 'proj_1',
    name: '需求分析完成',
    description: '完成所有需求的收集、分析和确认',
    status: MilestoneStatus.COMPLETED,
    dueDate: '2024-01-25T18:00:00Z',
    completedDate: '2024-01-24T16:30:00Z',
    deliverables: ['需求规格说明书', '用例图', '原型设计'],
    criteria: ['客户确认需求', '技术可行性评估完成', '项目范围明确'],
    dependencies: [],
    progress: 100,
    isKeyMilestone: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-24T16:30:00Z',
  },
  {
    id: 'milestone_2',
    projectId: 'proj_1',
    name: '系统设计评审',
    description: '完成系统架构设计和详细设计评审',
    status: MilestoneStatus.IN_PROGRESS,
    dueDate: '2024-02-05T18:00:00Z',
    deliverables: ['系统架构图', '数据库设计', '接口设计文档'],
    criteria: ['架构评审通过', '数据库设计确认', '接口规范制定'],
    dependencies: ['milestone_1'],
    progress: 75,
    isKeyMilestone: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-02-01T14:20:00Z',
  },
  {
    id: 'milestone_3',
    projectId: 'proj_1',
    name: '前端开发完成',
    description: '完成所有前端页面和组件开发',
    status: MilestoneStatus.UPCOMING,
    dueDate: '2024-02-28T18:00:00Z',
    deliverables: ['前端应用', '组件库', '用户手册'],
    criteria: ['所有页面开发完成', '功能测试通过', '用户体验评估'],
    dependencies: ['milestone_2'],
    progress: 0,
    isKeyMilestone: false,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: 'milestone_4',
    projectId: 'proj_1',
    name: '系统集成测试',
    description: '完成系统集成和端到端测试',
    status: MilestoneStatus.UPCOMING,
    dueDate: '2024-03-15T18:00:00Z',
    deliverables: ['测试报告', '缺陷修复记录', '性能测试报告'],
    criteria: ['集成测试通过', '性能指标达标', '安全测试完成'],
    dependencies: ['milestone_3'],
    progress: 0,
    isKeyMilestone: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
]

const MilestoneTracker: React.FC<MilestoneTrackerProps> = ({
  projectId,
  milestones = mockMilestones,
  onMilestoneUpdate,
  onMilestoneCreate,
  onMilestoneComplete,
  readonly = false,
}) => {
  const [selectedMilestone, setSelectedMilestone] = useState<Milestone | null>(null)
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [form] = Form.useForm()

  const getStatusColor = (status: MilestoneStatus) => {
    const colors = {
      [MilestoneStatus.UPCOMING]: 'default',
      [MilestoneStatus.IN_PROGRESS]: 'processing',
      [MilestoneStatus.COMPLETED]: 'success',
      [MilestoneStatus.OVERDUE]: 'error',
    }
    return colors[status]
  }

  const getStatusText = (status: MilestoneStatus) => {
    const texts = {
      [MilestoneStatus.UPCOMING]: '即将开始',
      [MilestoneStatus.IN_PROGRESS]: '进行中',
      [MilestoneStatus.COMPLETED]: '已完成',
      [MilestoneStatus.OVERDUE]: '已逾期',
    }
    return texts[status]
  }

  const getStatusIcon = (status: MilestoneStatus) => {
    const icons = {
      [MilestoneStatus.UPCOMING]: <ClockCircleOutlined />,
      [MilestoneStatus.IN_PROGRESS]: <ClockCircleOutlined />,
      [MilestoneStatus.COMPLETED]: <CheckCircleOutlined />,
      [MilestoneStatus.OVERDUE]: <ExclamationCircleOutlined />,
    }
    return icons[status]
  }

  const isOverdue = (milestone: Milestone) => {
    return new Date(milestone.dueDate) < new Date() && milestone.status !== MilestoneStatus.COMPLETED
  }

  const getDaysRemaining = (dueDate: string) => {
    const days = Math.ceil((new Date(dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    return days
  }

  const handleMilestoneComplete = (milestone: Milestone) => {
    Modal.confirm({
      title: '确认完成里程碑',
      content: `确定要标记"${milestone.name}"为已完成吗？`,
      onOk: () => {
        onMilestoneComplete(milestone.id)
      },
    })
  }

  const handleMilestoneEdit = (milestone: Milestone) => {
    setSelectedMilestone(milestone)
    form.setFieldsValue({
      name: milestone.name,
      description: milestone.description,
      dueDate: dayjs(milestone.dueDate),
      deliverables: milestone.deliverables,
      criteria: milestone.criteria,
      isKeyMilestone: milestone.isKeyMilestone,
    })
    setEditModalVisible(true)
  }

  const handleMilestoneUpdate = async (values: any) => {
    if (!selectedMilestone) return

    try {
      const updates = {
        name: values.name,
        description: values.description,
        dueDate: values.dueDate.toISOString(),
        deliverables: values.deliverables || [],
        criteria: values.criteria || [],
        isKeyMilestone: values.isKeyMilestone,
        updatedAt: new Date().toISOString(),
      }

      onMilestoneUpdate(selectedMilestone.id, updates)
      setEditModalVisible(false)
      form.resetFields()
    } catch (error) {
      console.error('Update milestone failed:', error)
    }
  }

  const handleMilestoneCreate = async (values: any) => {
    try {
      const newMilestone = {
        projectId,
        name: values.name,
        description: values.description,
        status: MilestoneStatus.UPCOMING,
        dueDate: values.dueDate.toISOString(),
        deliverables: values.deliverables || [],
        criteria: values.criteria || [],
        dependencies: [],
        progress: 0,
        isKeyMilestone: values.isKeyMilestone || false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      onMilestoneCreate(newMilestone)
      setCreateModalVisible(false)
      form.resetFields()
    } catch (error) {
      console.error('Create milestone failed:', error)
    }
  }

  const completedMilestones = milestones.filter(m => m.status === MilestoneStatus.COMPLETED).length
  const totalMilestones = milestones.length
  const overdueMilestones = milestones.filter(m => isOverdue(m)).length
  const upcomingMilestones = milestones.filter(m => m.status === MilestoneStatus.UPCOMING).length

  const renderTimelineItem = (milestone: Milestone) => {
    const daysRemaining = getDaysRemaining(milestone.dueDate)
    const overdue = isOverdue(milestone)

    return (
      <Timeline.Item
        key={milestone.id}
        dot={
          <Badge dot={milestone.isKeyMilestone}>
            {getStatusIcon(milestone.status)}
          </Badge>
        }
        color={overdue ? 'red' : getStatusColor(milestone.status)}
      >
        <div>
          <Space>
            <Text strong>{milestone.name}</Text>
            <Tag color={getStatusColor(milestone.status)}>
              {getStatusText(milestone.status)}
            </Tag>
            {milestone.isKeyMilestone && (
              <Tag color="gold" icon={<TrophyOutlined />}>
                关键里程碑
              </Tag>
            )}
          </Space>

          <div style={{ marginTop: 8 }}>
            <Text type="secondary">{milestone.description}</Text>
          </div>

          <div style={{ marginTop: 8 }}>
            <Space>
              <Text type="secondary">
                截止日期: {new Date(milestone.dueDate).toLocaleDateString('zh-CN')}
              </Text>
              {milestone.status !== MilestoneStatus.COMPLETED && (
                <Text type={overdue ? 'danger' : daysRemaining <= 3 ? 'warning' : 'secondary'}>
                  {overdue ? `逾期 ${Math.abs(daysRemaining)} 天` : `剩余 ${daysRemaining} 天`}
                </Text>
              )}
              {milestone.completedDate && (
                <Text type="success">
                  完成于: {new Date(milestone.completedDate).toLocaleDateString('zh-CN')}
                </Text>
              )}
            </Space>
          </div>

          {milestone.status === MilestoneStatus.IN_PROGRESS && (
            <div style={{ marginTop: 8 }}>
              <Progress percent={milestone.progress} size="small" />
            </div>
          )}

          <div style={{ marginTop: 12 }}>
            <Space>
              <Button
                size="small"
                onClick={() => {
                  setSelectedMilestone(milestone)
                  setDetailModalVisible(true)
                }}
              >
                查看详情
              </Button>
              {!readonly && (
                <>
                  <Button
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => handleMilestoneEdit(milestone)}
                  >
                    编辑
                  </Button>
                  {milestone.status !== MilestoneStatus.COMPLETED && (
                    <Button
                      size="small"
                      type="primary"
                      icon={<CheckCircleOutlined />}
                      onClick={() => handleMilestoneComplete(milestone)}
                    >
                      标记完成
                    </Button>
                  )}
                </>
              )}
            </Space>
          </div>
        </div>
      </Timeline.Item>
    )
  }

  return (
    <div>
      {/* Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总里程碑"
              value={totalMilestones}
              prefix={<FlagOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已完成"
              value={completedMilestones}
              prefix={<CheckCircleOutlined />}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="即将到来"
              value={upcomingMilestones}
              prefix={<ClockCircleOutlined />}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已逾期"
              value={overdueMilestones}
              prefix={<WarningOutlined />}
              suffix="个"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Progress Overview */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Title level={5} style={{ margin: 0 }}>项目进度</Title>
          <Text>{completedMilestones}/{totalMilestones} 里程碑已完成</Text>
        </div>
        <Progress
          percent={Math.round((completedMilestones / totalMilestones) * 100)}
          strokeColor="#52c41a"
          trailColor="#f0f0f0"
        />
      </Card>

      {/* Alerts */}
      {overdueMilestones > 0 && (
        <Alert
          message="里程碑逾期警告"
          description={`当前有 ${overdueMilestones} 个里程碑已逾期，请及时处理`}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* Timeline */}
      <Card
        title="里程碑时间线"
        extra={
          !readonly && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              添加里程碑
            </Button>
          )
        }
      >
        <Timeline mode="left">
          {milestones.map(renderTimelineItem)}
        </Timeline>
      </Card>

      {/* Milestone Detail Modal */}
      <Modal
        title="里程碑详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedMilestone && (
          <div>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Title level={4}>{selectedMilestone.name}</Title>
                <Space>
                  <Tag color={getStatusColor(selectedMilestone.status)}>
                    {getStatusText(selectedMilestone.status)}
                  </Tag>
                  {selectedMilestone.isKeyMilestone && (
                    <Tag color="gold" icon={<TrophyOutlined />}>
                      关键里程碑
                    </Tag>
                  )}
                </Space>
              </div>

              <div>
                <Text>{selectedMilestone.description}</Text>
              </div>

              <div>
                <Title level={5}>交付物</Title>
                <List
                  size="small"
                  dataSource={selectedMilestone.deliverables}
                  renderItem={item => <List.Item>• {item}</List.Item>}
                />
              </div>

              <div>
                <Title level={5}>完成标准</Title>
                <List
                  size="small"
                  dataSource={selectedMilestone.criteria}
                  renderItem={item => <List.Item>• {item}</List.Item>}
                />
              </div>

              <div>
                <Space>
                  <Text strong>截止日期:</Text>
                  <Text>{new Date(selectedMilestone.dueDate).toLocaleString('zh-CN')}</Text>
                </Space>
              </div>

              {selectedMilestone.completedDate && (
                <div>
                  <Space>
                    <Text strong>完成时间:</Text>
                    <Text type="success">
                      {new Date(selectedMilestone.completedDate).toLocaleString('zh-CN')}
                    </Text>
                  </Space>
                </div>
              )}
            </Space>
          </div>
        )}
      </Modal>

      {/* Edit Milestone Modal */}
      <Modal
        title="编辑里程碑"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} onFinish={handleMilestoneUpdate} layout="vertical">
          <Form.Item
            name="name"
            label="里程碑名称"
            rules={[{ required: true, message: '请输入里程碑名称' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea rows={3} />
          </Form.Item>

          <Form.Item
            name="dueDate"
            label="截止日期"
            rules={[{ required: true, message: '请选择截止日期' }]}
          >
            <DatePicker showTime style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="deliverables" label="交付物">
            <Select mode="tags" placeholder="输入交付物" />
          </Form.Item>

          <Form.Item name="criteria" label="完成标准">
            <Select mode="tags" placeholder="输入完成标准" />
          </Form.Item>

          <Form.Item name="isKeyMilestone" label="关键里程碑">
            <Select>
              <Option value={true}>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* Create Milestone Modal */}
      <Modal
        title="创建里程碑"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} onFinish={handleMilestoneCreate} layout="vertical">
          <Form.Item
            name="name"
            label="里程碑名称"
            rules={[{ required: true, message: '请输入里程碑名称' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea rows={3} />
          </Form.Item>

          <Form.Item
            name="dueDate"
            label="截止日期"
            rules={[{ required: true, message: '请选择截止日期' }]}
          >
            <DatePicker showTime style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="deliverables" label="交付物">
            <Select mode="tags" placeholder="输入交付物" />
          </Form.Item>

          <Form.Item name="criteria" label="完成标准">
            <Select mode="tags" placeholder="输入完成标准" />
          </Form.Item>

          <Form.Item name="isKeyMilestone" label="关键里程碑" initialValue={false}>
            <Select>
              <Option value={true}>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default MilestoneTracker
