import React, { useState, useRef, useEffect } from 'react'
import {
  Card,
  Button,
  Space,
  Select,
  Typography,
  Tooltip,
  Tag,
  Progress,
  Modal,
  Form,
  Input,
  DatePicker,
  message,
} from 'antd'
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined,
  CalendarOutlined,
  EditOutlined,
  PlusOutlined,
} from '@ant-design/icons'
import { GanttTask, GanttViewType, Task, Milestone } from '../../types/projects'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

interface GanttChartProps {
  tasks: Task[]
  milestones: Milestone[]
  onTaskUpdate: (taskId: string, updates: Partial<Task>) => void
  onTaskCreate: (task: Omit<Task, 'id'>) => void
  readonly?: boolean
}

// Mock Gantt tasks
const mockGanttTasks: GanttTask[] = [
  {
    id: 'task_1',
    name: '需求分析',
    start: new Date('2024-01-15'),
    end: new Date('2024-01-25'),
    progress: 100,
    dependencies: [],
    type: 'task',
    styles: {
      backgroundColor: '#1890ff',
      progressColor: '#52c41a',
    },
  },
  {
    id: 'task_2',
    name: '系统设计',
    start: new Date('2024-01-26'),
    end: new Date('2024-02-05'),
    progress: 80,
    dependencies: ['task_1'],
    type: 'task',
    styles: {
      backgroundColor: '#722ed1',
      progressColor: '#52c41a',
    },
  },
  {
    id: 'milestone_1',
    name: '设计评审',
    start: new Date('2024-02-06'),
    end: new Date('2024-02-06'),
    progress: 0,
    dependencies: ['task_2'],
    type: 'milestone',
    styles: {
      backgroundColor: '#faad14',
    },
  },
  {
    id: 'task_3',
    name: '前端开发',
    start: new Date('2024-02-07'),
    end: new Date('2024-02-28'),
    progress: 45,
    dependencies: ['milestone_1'],
    type: 'task',
    styles: {
      backgroundColor: '#13c2c2',
      progressColor: '#52c41a',
    },
  },
  {
    id: 'task_4',
    name: '后端开发',
    start: new Date('2024-02-07'),
    end: new Date('2024-03-05'),
    progress: 30,
    dependencies: ['milestone_1'],
    type: 'task',
    styles: {
      backgroundColor: '#eb2f96',
      progressColor: '#52c41a',
    },
  },
]

const GanttChart: React.FC<GanttChartProps> = ({
  tasks = [],
  milestones = [],
  onTaskUpdate,
  onTaskCreate,
  readonly = false,
}) => {
  const [ganttTasks, setGanttTasks] = useState<GanttTask[]>(mockGanttTasks)
  const [viewType, setViewType] = useState<GanttViewType>({
    viewMode: 'week',
    columnWidth: 100,
  })
  const [selectedTask, setSelectedTask] = useState<GanttTask | null>(null)
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [form] = Form.useForm()
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    drawGanttChart()
  }, [ganttTasks, viewType])

  const drawGanttChart = () => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Set canvas size
    canvas.width = 1200
    canvas.height = ganttTasks.length * 40 + 100

    // Draw timeline header
    drawTimelineHeader(ctx)

    // Draw tasks
    ganttTasks.forEach((task, index) => {
      drawTask(ctx, task, index)
    })

    // Draw dependencies
    drawDependencies(ctx)
  }

  const drawTimelineHeader = (ctx: CanvasRenderingContext2D) => {
    const startDate = new Date(Math.min(...ganttTasks.map(t => t.start.getTime())))
    const endDate = new Date(Math.max(...ganttTasks.map(t => t.end.getTime())))
    
    ctx.fillStyle = '#f0f0f0'
    ctx.fillRect(0, 0, 1200, 60)
    
    ctx.fillStyle = '#000'
    ctx.font = '12px Arial'
    
    // Draw date labels based on view mode
    const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
    const step = viewType.viewMode === 'day' ? 1 : viewType.viewMode === 'week' ? 7 : 30
    
    for (let i = 0; i <= daysDiff; i += step) {
      const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000)
      const x = 200 + (i / daysDiff) * 1000
      
      ctx.fillText(
        date.toLocaleDateString('zh-CN', { 
          month: 'short', 
          day: 'numeric' 
        }),
        x,
        20
      )
      
      // Draw grid lines
      ctx.strokeStyle = '#e0e0e0'
      ctx.beginPath()
      ctx.moveTo(x, 60)
      ctx.lineTo(x, canvas.height)
      ctx.stroke()
    }
  }

  const drawTask = (ctx: CanvasRenderingContext2D, task: GanttTask, index: number) => {
    const y = 80 + index * 40
    const startDate = new Date(Math.min(...ganttTasks.map(t => t.start.getTime())))
    const endDate = new Date(Math.max(...ganttTasks.map(t => t.end.getTime())))
    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
    
    // Calculate task position
    const taskStartDays = Math.ceil((task.start.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
    const taskDuration = Math.ceil((task.end.getTime() - task.start.getTime()) / (1000 * 60 * 60 * 24))
    
    const x = 200 + (taskStartDays / totalDays) * 1000
    const width = (taskDuration / totalDays) * 1000
    
    // Draw task name
    ctx.fillStyle = '#000'
    ctx.font = '12px Arial'
    ctx.fillText(task.name, 10, y + 15)
    
    if (task.type === 'milestone') {
      // Draw milestone diamond
      ctx.fillStyle = task.styles?.backgroundColor || '#faad14'
      ctx.beginPath()
      ctx.moveTo(x, y + 5)
      ctx.lineTo(x + 10, y + 15)
      ctx.lineTo(x, y + 25)
      ctx.lineTo(x - 10, y + 15)
      ctx.closePath()
      ctx.fill()
    } else {
      // Draw task bar
      ctx.fillStyle = task.styles?.backgroundColor || '#1890ff'
      ctx.fillRect(x, y + 5, width, 20)
      
      // Draw progress
      if (task.progress > 0) {
        ctx.fillStyle = task.styles?.progressColor || '#52c41a'
        ctx.fillRect(x, y + 5, width * (task.progress / 100), 20)
      }
      
      // Draw task border
      ctx.strokeStyle = '#fff'
      ctx.lineWidth = 1
      ctx.strokeRect(x, y + 5, width, 20)
      
      // Draw progress text
      ctx.fillStyle = '#fff'
      ctx.font = '10px Arial'
      ctx.fillText(`${task.progress}%`, x + 5, y + 18)
    }
  }

  const drawDependencies = (ctx: CanvasRenderingContext2D) => {
    ganttTasks.forEach((task, index) => {
      task.dependencies.forEach(depId => {
        const depIndex = ganttTasks.findIndex(t => t.id === depId)
        if (depIndex !== -1) {
          const startDate = new Date(Math.min(...ganttTasks.map(t => t.start.getTime())))
          const endDate = new Date(Math.max(...ganttTasks.map(t => t.end.getTime())))
          const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
          
          const depTask = ganttTasks[depIndex]
          const depEndDays = Math.ceil((depTask.end.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
          const taskStartDays = Math.ceil((task.start.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
          
          const x1 = 200 + (depEndDays / totalDays) * 1000
          const y1 = 80 + depIndex * 40 + 15
          const x2 = 200 + (taskStartDays / totalDays) * 1000
          const y2 = 80 + index * 40 + 15
          
          // Draw dependency arrow
          ctx.strokeStyle = '#ff4d4f'
          ctx.lineWidth = 2
          ctx.beginPath()
          ctx.moveTo(x1, y1)
          ctx.lineTo(x2 - 10, y2)
          ctx.stroke()
          
          // Draw arrow head
          ctx.fillStyle = '#ff4d4f'
          ctx.beginPath()
          ctx.moveTo(x2, y2)
          ctx.lineTo(x2 - 10, y2 - 5)
          ctx.lineTo(x2 - 10, y2 + 5)
          ctx.closePath()
          ctx.fill()
        }
      })
    })
  }

  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (readonly) return
    
    const canvas = canvasRef.current
    if (!canvas) return
    
    const rect = canvas.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top
    
    // Find clicked task
    const taskIndex = Math.floor((y - 80) / 40)
    if (taskIndex >= 0 && taskIndex < ganttTasks.length) {
      const task = ganttTasks[taskIndex]
      setSelectedTask(task)
      setEditModalVisible(true)
    }
  }

  const handleTaskUpdate = async (values: any) => {
    if (!selectedTask) return
    
    try {
      const updatedTask: GanttTask = {
        ...selectedTask,
        name: values.name,
        start: values.dateRange[0].toDate(),
        end: values.dateRange[1].toDate(),
        progress: values.progress,
      }
      
      setGanttTasks(prev => 
        prev.map(task => task.id === selectedTask.id ? updatedTask : task)
      )
      
      setEditModalVisible(false)
      message.success('任务已更新')
    } catch (error) {
      message.error('更新失败')
    }
  }

  const handleTaskCreate = async (values: any) => {
    try {
      const newTask: GanttTask = {
        id: `task_${Date.now()}`,
        name: values.name,
        start: values.dateRange[0].toDate(),
        end: values.dateRange[1].toDate(),
        progress: 0,
        dependencies: [],
        type: 'task',
        styles: {
          backgroundColor: '#1890ff',
          progressColor: '#52c41a',
        },
      }
      
      setGanttTasks(prev => [...prev, newTask])
      setCreateModalVisible(false)
      message.success('任务已创建')
    } catch (error) {
      message.error('创建失败')
    }
  }

  const handleZoomIn = () => {
    setViewType(prev => ({
      ...prev,
      columnWidth: Math.min(prev.columnWidth * 1.2, 200),
    }))
  }

  const handleZoomOut = () => {
    setViewType(prev => ({
      ...prev,
      columnWidth: Math.max(prev.columnWidth * 0.8, 50),
    }))
  }

  return (
    <div>
      {/* Toolbar */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Space>
          <Select
            value={viewType.viewMode}
            onChange={(value) => setViewType(prev => ({ ...prev, viewMode: value }))}
            style={{ width: 100 }}
          >
            <Option value="day">日</Option>
            <Option value="week">周</Option>
            <Option value="month">月</Option>
          </Select>
          
          <Button icon={<ZoomInOutlined />} onClick={handleZoomIn}>
            放大
          </Button>
          
          <Button icon={<ZoomOutOutlined />} onClick={handleZoomOut}>
            缩小
          </Button>
          
          <Button icon={<FullscreenOutlined />}>
            全屏
          </Button>
          
          {!readonly && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              添加任务
            </Button>
          )}
        </Space>
      </Card>

      {/* Gantt Chart */}
      <Card>
        <div style={{ overflow: 'auto', maxHeight: '600px' }}>
          <canvas
            ref={canvasRef}
            onClick={handleCanvasClick}
            style={{ cursor: readonly ? 'default' : 'pointer' }}
          />
        </div>
      </Card>

      {/* Task Legend */}
      <Card title="图例" size="small" style={{ marginTop: 16 }}>
        <Space wrap>
          <Space>
            <div style={{ width: 20, height: 15, backgroundColor: '#1890ff' }} />
            <Text>任务</Text>
          </Space>
          <Space>
            <div style={{ width: 15, height: 15, backgroundColor: '#faad14', transform: 'rotate(45deg)' }} />
            <Text>里程碑</Text>
          </Space>
          <Space>
            <div style={{ width: 20, height: 15, backgroundColor: '#52c41a' }} />
            <Text>已完成</Text>
          </Space>
          <Space>
            <div style={{ width: 20, height: 2, backgroundColor: '#ff4d4f' }} />
            <Text>依赖关系</Text>
          </Space>
        </Space>
      </Card>

      {/* Edit Task Modal */}
      <Modal
        title="编辑任务"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form
          form={form}
          onFinish={handleTaskUpdate}
          layout="vertical"
          initialValues={selectedTask ? {
            name: selectedTask.name,
            dateRange: [dayjs(selectedTask.start), dayjs(selectedTask.end)],
            progress: selectedTask.progress,
          } : {}}
        >
          <Form.Item
            name="name"
            label="任务名称"
            rules={[{ required: true, message: '请输入任务名称' }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="dateRange"
            label="时间范围"
            rules={[{ required: true, message: '请选择时间范围' }]}
          >
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="progress"
            label="进度"
            rules={[{ required: true, message: '请输入进度' }]}
          >
            <Input type="number" min={0} max={100} addonAfter="%" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Create Task Modal */}
      <Modal
        title="创建任务"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form
          form={form}
          onFinish={handleTaskCreate}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="任务名称"
            rules={[{ required: true, message: '请输入任务名称' }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="dateRange"
            label="时间范围"
            rules={[{ required: true, message: '请选择时间范围' }]}
          >
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default GanttChart
