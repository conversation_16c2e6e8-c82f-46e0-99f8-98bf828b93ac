import React, { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Tag,
  Avatar,
  Space,
  Typography,
  Button,
  List,
  Timeline,
  Alert,
  <PERSON>lt<PERSON>,
  Badge,
} from 'antd'
import {
  ProjectOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  TeamOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  Exclamation<PERSON>ircleOutlined,
  CalendarOutlined,
  TrophyOutlined,
  RiseOutlined,
} from '@ant-design/icons'
import { Project, ProjectStatus, Milestone, Task, ProjectDashboard as DashboardData } from '../../types/projects'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography

interface ProjectDashboardProps {
  data: DashboardData
  onProjectClick: (projectId: string) => void
  onTaskClick: (taskId: string) => void
  onMilestoneClick: (milestoneId: string) => void
}

// Mock dashboard data
const mockDashboardData: DashboardData = {
  overview: {
    totalProjects: 24,
    activeProjects: 8,
    completedProjects: 14,
    delayedProjects: 2,
    onTimeDeliveryRate: 87.5,
    budgetUtilization: 78.3,
    teamUtilization: 82.1,
    averageProjectDuration: 45,
    customerSatisfaction: 4.6,
  },
  recentProjects: [
    {
      id: 'proj_1',
      name: '企业管理系统',
      status: ProjectStatus.IN_PROGRESS,
      progress: 65,
      endDate: '2024-02-15',
      clientName: '张三公司',
      managerName: '王项目经理',
    } as Project,
    {
      id: 'proj_2',
      name: '电商平台开发',
      status: ProjectStatus.IN_PROGRESS,
      progress: 40,
      endDate: '2024-03-01',
      clientName: '李四企业',
      managerName: '赵项目经理',
    } as Project,
  ],
  upcomingMilestones: [
    {
      id: 'milestone_1',
      projectId: 'proj_1',
      name: '系统测试完成',
      dueDate: '2024-01-25',
      status: 'upcoming' as any,
      progress: 80,
    } as Milestone,
    {
      id: 'milestone_2',
      projectId: 'proj_2',
      name: '前端开发完成',
      dueDate: '2024-01-30',
      status: 'in_progress' as any,
      progress: 60,
    } as Milestone,
  ],
  overdueTasks: [
    {
      id: 'task_1',
      projectId: 'proj_1',
      name: '数据库优化',
      assigneeName: '王工程师',
      endDate: '2024-01-20',
      priority: 'high' as any,
    } as Task,
  ],
  teamWorkload: [
    {
      userId: '4',
      userName: '王工程师',
      department: '技术部',
      totalHours: 160,
      allocatedHours: 140,
      utilization: 87.5,
      activeProjects: 3,
      overdueTasks: 1,
    },
    {
      userId: '5',
      userName: '赵项目经理',
      department: '项目部',
      totalHours: 160,
      allocatedHours: 120,
      utilization: 75,
      activeProjects: 2,
      overdueTasks: 0,
    },
  ],
  budgetSummary: {
    totalBudget: 1200000,
    spentBudget: 940000,
    remainingBudget: 260000,
    projectedCost: 1150000,
    budgetUtilization: 78.3,
    costVariance: -50000,
  },
  riskSummary: {
    totalRisks: 12,
    highRisks: 2,
    mediumRisks: 5,
    lowRisks: 5,
    mitigatedRisks: 8,
    openRisks: 4,
  },
}

const ProjectDashboard: React.FC<ProjectDashboardProps> = ({
  data = mockDashboardData,
  onProjectClick,
  onTaskClick,
  onMilestoneClick,
}) => {
  const [timeRange, setTimeRange] = useState('month')

  const getStatusColor = (status: ProjectStatus) => {
    const colors = {
      [ProjectStatus.PLANNING]: 'blue',
      [ProjectStatus.IN_PROGRESS]: 'processing',
      [ProjectStatus.ON_HOLD]: 'warning',
      [ProjectStatus.COMPLETED]: 'success',
      [ProjectStatus.CANCELLED]: 'error',
      [ProjectStatus.DELAYED]: 'error',
    }
    return colors[status]
  }

  const getStatusText = (status: ProjectStatus) => {
    const texts = {
      [ProjectStatus.PLANNING]: '规划中',
      [ProjectStatus.IN_PROGRESS]: '进行中',
      [ProjectStatus.ON_HOLD]: '暂停',
      [ProjectStatus.COMPLETED]: '已完成',
      [ProjectStatus.CANCELLED]: '已取消',
      [ProjectStatus.DELAYED]: '延期',
    }
    return texts[status]
  }

  const getPriorityColor = (priority: string) => {
    const colors = {
      low: 'green',
      medium: 'orange',
      high: 'red',
      critical: 'purple',
    }
    return colors[priority as keyof typeof colors]
  }

  const formatCurrency = (amount: number) => {
    return `¥${amount.toLocaleString()}`
  }

  const projectColumns: ColumnsType<Project> = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <Button type="link" onClick={() => onProjectClick(record.id)}>
            {text}
          </Button>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.clientName}
          </Text>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress) => (
        <Progress percent={progress} size="small" />
      ),
    },
    {
      title: '截止日期',
      dataIndex: 'endDate',
      key: 'endDate',
      render: (date) => new Date(date).toLocaleDateString('zh-CN'),
    },
    {
      title: '项目经理',
      dataIndex: 'managerName',
      key: 'managerName',
    },
  ]

  const milestoneColumns: ColumnsType<Milestone> = [
    {
      title: '里程碑',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Button type="link" onClick={() => onMilestoneClick(record.id)}>
          {text}
        </Button>
      ),
    },
    {
      title: '截止日期',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (date) => {
        const isOverdue = new Date(date) < new Date()
        return (
          <Text type={isOverdue ? 'danger' : 'secondary'}>
            {new Date(date).toLocaleDateString('zh-CN')}
          </Text>
        )
      },
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress) => (
        <Progress percent={progress} size="small" />
      ),
    },
  ]

  const taskColumns: ColumnsType<Task> = [
    {
      title: '任务',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <Button type="link" onClick={() => onTaskClick(record.id)}>
            {text}
          </Button>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.assigneeName}
          </Text>
        </div>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => (
        <Tag color={getPriorityColor(priority)}>
          {priority}
        </Tag>
      ),
    },
    {
      title: '逾期天数',
      dataIndex: 'endDate',
      key: 'overdue',
      render: (date) => {
        const overdueDays = Math.ceil((new Date().getTime() - new Date(date).getTime()) / (1000 * 60 * 60 * 24))
        return (
          <Text type="danger">
            {overdueDays} 天
          </Text>
        )
      },
    },
  ]

  return (
    <div>
      {/* Overview Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总项目数"
              value={data.overview.totalProjects}
              prefix={<ProjectOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="进行中项目"
              value={data.overview.activeProjects}
              prefix={<ClockCircleOutlined />}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="按时交付率"
              value={data.overview.onTimeDeliveryRate}
              prefix={<CheckCircleOutlined />}
              suffix="%"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="预算使用率"
              value={data.overview.budgetUtilization}
              prefix={<DollarOutlined />}
              suffix="%"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Key Metrics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="团队利用率"
              value={data.overview.teamUtilization}
              prefix={<TeamOutlined />}
              suffix="%"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="平均项目周期"
              value={data.overview.averageProjectDuration}
              prefix={<CalendarOutlined />}
              suffix="天"
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="客户满意度"
              value={data.overview.customerSatisfaction}
              prefix={<TrophyOutlined />}
              suffix="/5"
              precision={1}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="延期项目"
              value={data.overview.delayedProjects}
              prefix={<WarningOutlined />}
              suffix="个"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Row gutter={[16, 16]}>
        {/* Recent Projects */}
        <Col xs={24} lg={12}>
          <Card
            title="最近项目"
            extra={
              <Button type="link" onClick={() => onProjectClick('all')}>
                查看全部
              </Button>
            }
          >
            <Table
              columns={projectColumns}
              dataSource={data.recentProjects}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>

        {/* Budget Summary */}
        <Col xs={24} lg={12}>
          <Card title="预算概览">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                  <Text>总预算</Text>
                  <Text strong>{formatCurrency(data.budgetSummary.totalBudget)}</Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                  <Text>已使用</Text>
                  <Text>{formatCurrency(data.budgetSummary.spentBudget)}</Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                  <Text>剩余预算</Text>
                  <Text type={data.budgetSummary.remainingBudget < 0 ? 'danger' : 'success'}>
                    {formatCurrency(data.budgetSummary.remainingBudget)}
                  </Text>
                </div>
                <Progress
                  percent={data.budgetSummary.budgetUtilization}
                  strokeColor={data.budgetSummary.budgetUtilization > 90 ? '#ff4d4f' : '#52c41a'}
                />
              </div>

              <div>
                <Text strong>成本差异: </Text>
                <Text type={data.budgetSummary.costVariance > 0 ? 'danger' : 'success'}>
                  {formatCurrency(data.budgetSummary.costVariance)}
                </Text>
              </div>
            </Space>
          </Card>
        </Col>

        {/* Upcoming Milestones */}
        <Col xs={24} lg={12}>
          <Card title="即将到来的里程碑">
            <Table
              columns={milestoneColumns}
              dataSource={data.upcomingMilestones}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>

        {/* Team Workload */}
        <Col xs={24} lg={12}>
          <Card title="团队工作负载">
            <List
              dataSource={data.teamWorkload}
              renderItem={(member) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar>{member.userName[0]}</Avatar>}
                    title={
                      <Space>
                        <span>{member.userName}</span>
                        <Tag size="small">{member.department}</Tag>
                      </Space>
                    }
                    description={
                      <div>
                        <div style={{ marginBottom: 4 }}>
                          <Text style={{ fontSize: 12 }}>
                            利用率: {member.utilization}% ({member.allocatedHours}/{member.totalHours}h)
                          </Text>
                        </div>
                        <Progress percent={member.utilization} size="small" />
                        <div style={{ marginTop: 4 }}>
                          <Space>
                            <Text style={{ fontSize: 12 }}>
                              项目: {member.activeProjects}
                            </Text>
                            {member.overdueTasks > 0 && (
                              <Text type="danger" style={{ fontSize: 12 }}>
                                逾期: {member.overdueTasks}
                              </Text>
                            )}
                          </Space>
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* Overdue Tasks */}
        {data.overdueTasks.length > 0 && (
          <Col xs={24} lg={12}>
            <Card title="逾期任务" extra={<Badge count={data.overdueTasks.length} />}>
              <Table
                columns={taskColumns}
                dataSource={data.overdueTasks}
                rowKey="id"
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
        )}

        {/* Risk Summary */}
        <Col xs={24} lg={12}>
          <Card title="风险概览">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="高风险"
                  value={data.riskSummary.highRisks}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="中风险"
                  value={data.riskSummary.mediumRisks}
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="低风险"
                  value={data.riskSummary.lowRisks}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="已缓解"
                  value={data.riskSummary.mitigatedRisks}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
            </Row>
            
            {data.riskSummary.highRisks > 0 && (
              <Alert
                message="高风险警告"
                description={`当前有 ${data.riskSummary.highRisks} 个高风险项目需要关注`}
                type="warning"
                showIcon
                style={{ marginTop: 16 }}
              />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default ProjectDashboard
