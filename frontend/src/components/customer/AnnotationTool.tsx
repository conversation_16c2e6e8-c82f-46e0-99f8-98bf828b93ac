import React, { useState, useRef, useEffect } from 'react'
import {
  Card,
  Button,
  Input,
  Select,
  Space,
  Typography,
  Tag,
  Avatar,
  List,
  Modal,
  Form,
  Rate,
  Tooltip,
  Popover,
  Badge,
  Divider,
} from 'antd'
import {
  CommentOutlined,
  BulbOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  QuestionCircleOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReplyArrowIcon,
  CloseOutlined,
} from '@ant-design/icons'
import { ReviewAnnotation, AnnotationType, AnnotationPosition } from '../../types/customerReview'

const { Text, Title } = Typography
const { TextArea } = Input
const { Option } = Select

interface AnnotationToolProps {
  annotations: ReviewAnnotation[]
  onAddAnnotation: (annotation: Omit<ReviewAnnotation, 'id' | 'createdAt' | 'updatedAt'>) => void
  onUpdateAnnotation: (id: string, updates: Partial<ReviewAnnotation>) => void
  onDeleteAnnotation: (id: string) => void
  onResolveAnnotation: (id: string) => void
  currentUserId: string
  currentUserName: string
  readonly?: boolean
}

// Mock annotations
const mockAnnotations: ReviewAnnotation[] = [
  {
    id: 'annotation_1',
    type: AnnotationType.COMMENT,
    title: '界面布局建议',
    content: '建议将导航菜单放在左侧，这样更符合用户习惯。',
    position: { x: 150, y: 200, viewMode: 'overview' as any },
    authorId: '2',
    authorName: '张三',
    targetElement: 'navigation',
    priority: 'medium',
    status: 'open',
    responses: [
      {
        id: 'response_1',
        content: '好的建议，我们会考虑调整布局设计。',
        authorId: '4',
        authorName: '王工程师',
        createdAt: '2024-01-15T11:30:00Z',
      },
    ],
    attachments: [],
    createdAt: '2024-01-15T11:00:00Z',
    updatedAt: '2024-01-15T11:30:00Z',
  },
  {
    id: 'annotation_2',
    type: AnnotationType.ISSUE,
    title: '性能问题',
    content: '担心数据量大时系统响应速度，建议增加缓存机制。',
    position: { x: 300, y: 150, viewMode: 'detailed' as any },
    authorId: '2',
    authorName: '张三',
    targetComponent: 'database',
    priority: 'high',
    status: 'open',
    responses: [],
    attachments: [],
    createdAt: '2024-01-15T12:00:00Z',
    updatedAt: '2024-01-15T12:00:00Z',
  },
]

const AnnotationTool: React.FC<AnnotationToolProps> = ({
  annotations = mockAnnotations,
  onAddAnnotation,
  onUpdateAnnotation,
  onDeleteAnnotation,
  onResolveAnnotation,
  currentUserId = '2',
  currentUserName = '张三',
  readonly = false,
}) => {
  const [isCreating, setIsCreating] = useState(false)
  const [selectedAnnotation, setSelectedAnnotation] = useState<ReviewAnnotation | null>(null)
  const [newAnnotation, setNewAnnotation] = useState({
    type: AnnotationType.COMMENT,
    title: '',
    content: '',
    priority: 'medium' as 'low' | 'medium' | 'high',
    position: { x: 0, y: 0, viewMode: 'overview' as any },
  })
  const [replyText, setReplyText] = useState('')
  const [form] = Form.useForm()

  const getTypeIcon = (type: AnnotationType) => {
    const icons = {
      [AnnotationType.COMMENT]: <CommentOutlined />,
      [AnnotationType.SUGGESTION]: <BulbOutlined />,
      [AnnotationType.ISSUE]: <ExclamationCircleOutlined />,
      [AnnotationType.APPROVAL]: <CheckCircleOutlined />,
      [AnnotationType.QUESTION]: <QuestionCircleOutlined />,
    }
    return icons[type]
  }

  const getTypeColor = (type: AnnotationType) => {
    const colors = {
      [AnnotationType.COMMENT]: '#1890ff',
      [AnnotationType.SUGGESTION]: '#52c41a',
      [AnnotationType.ISSUE]: '#ff4d4f',
      [AnnotationType.APPROVAL]: '#722ed1',
      [AnnotationType.QUESTION]: '#faad14',
    }
    return colors[type]
  }

  const getTypeText = (type: AnnotationType) => {
    const texts = {
      [AnnotationType.COMMENT]: '评论',
      [AnnotationType.SUGGESTION]: '建议',
      [AnnotationType.ISSUE]: '问题',
      [AnnotationType.APPROVAL]: '批准',
      [AnnotationType.QUESTION]: '疑问',
    }
    return texts[type]
  }

  const getPriorityColor = (priority: string) => {
    const colors = {
      low: 'green',
      medium: 'orange',
      high: 'red',
    }
    return colors[priority as keyof typeof colors]
  }

  const getPriorityText = (priority: string) => {
    const texts = {
      low: '低',
      medium: '中',
      high: '高',
    }
    return texts[priority as keyof typeof texts]
  }

  const handleCreateAnnotation = () => {
    if (!newAnnotation.title || !newAnnotation.content) return

    const annotation = {
      type: newAnnotation.type,
      title: newAnnotation.title,
      content: newAnnotation.content,
      position: newAnnotation.position,
      authorId: currentUserId,
      authorName: currentUserName,
      priority: newAnnotation.priority,
      status: 'open' as const,
      responses: [],
      attachments: [],
    }

    onAddAnnotation(annotation)
    setIsCreating(false)
    setNewAnnotation({
      type: AnnotationType.COMMENT,
      title: '',
      content: '',
      priority: 'medium',
      position: { x: 0, y: 0, viewMode: 'overview' as any },
    })
    form.resetFields()
  }

  const handleAddReply = (annotationId: string) => {
    if (!replyText.trim()) return

    const annotation = annotations.find(a => a.id === annotationId)
    if (!annotation) return

    const newResponse = {
      id: `response_${Date.now()}`,
      content: replyText,
      authorId: currentUserId,
      authorName: currentUserName,
      createdAt: new Date().toISOString(),
    }

    onUpdateAnnotation(annotationId, {
      responses: [...annotation.responses, newResponse],
      updatedAt: new Date().toISOString(),
    })

    setReplyText('')
  }

  const renderAnnotationMarker = (annotation: ReviewAnnotation) => (
    <div
      key={annotation.id}
      style={{
        position: 'absolute',
        left: annotation.position.x,
        top: annotation.position.y,
        zIndex: 1000,
      }}
    >
      <Popover
        content={
          <div style={{ maxWidth: 300 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Space>
                  <Tag color={getTypeColor(annotation.type)} icon={getTypeIcon(annotation.type)}>
                    {getTypeText(annotation.type)}
                  </Tag>
                  <Tag color={getPriorityColor(annotation.priority)}>
                    {getPriorityText(annotation.priority)}
                  </Tag>
                </Space>
              </div>
              
              <div>
                <Text strong>{annotation.title}</Text>
                <br />
                <Text>{annotation.content}</Text>
              </div>

              <div>
                <Space>
                  <Avatar size="small" />
                  <Text type="secondary">{annotation.authorName}</Text>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {new Date(annotation.createdAt).toLocaleString('zh-CN')}
                  </Text>
                </Space>
              </div>

              {annotation.responses.length > 0 && (
                <div>
                  <Divider style={{ margin: '8px 0' }} />
                  <Text strong style={{ fontSize: 12 }}>回复 ({annotation.responses.length})</Text>
                  {annotation.responses.slice(-2).map(response => (
                    <div key={response.id} style={{ marginTop: 8 }}>
                      <Space>
                        <Avatar size="small" />
                        <Text style={{ fontSize: 12 }}>{response.authorName}</Text>
                      </Space>
                      <div style={{ marginLeft: 24, fontSize: 12 }}>
                        {response.content}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              <div>
                <Space>
                  <Button
                    size="small"
                    onClick={() => setSelectedAnnotation(annotation)}
                  >
                    详情
                  </Button>
                  {annotation.status === 'open' && !readonly && (
                    <Button
                      size="small"
                      type="primary"
                      onClick={() => onResolveAnnotation(annotation.id)}
                    >
                      解决
                    </Button>
                  )}
                </Space>
              </div>
            </Space>
          </div>
        }
        title={null}
        trigger="click"
      >
        <Badge
          count={annotation.responses.length}
          size="small"
          offset={[8, -8]}
        >
          <Button
            shape="circle"
            size="small"
            icon={getTypeIcon(annotation.type)}
            style={{
              backgroundColor: getTypeColor(annotation.type),
              borderColor: getTypeColor(annotation.type),
              color: 'white',
              boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
            }}
          />
        </Badge>
      </Popover>
    </div>
  )

  return (
    <div>
      {/* Annotation Markers */}
      {annotations.map(renderAnnotationMarker)}

      {/* Annotation List Panel */}
      <Card
        title={
          <Space>
            <CommentOutlined />
            <span>注释 ({annotations.length})</span>
          </Space>
        }
        extra={
          !readonly && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsCreating(true)}
            >
              添加注释
            </Button>
          )
        }
        style={{ marginTop: 16 }}
      >
        <List
          dataSource={annotations}
          renderItem={(annotation) => (
            <List.Item
              actions={[
                <Button
                  type="text"
                  size="small"
                  onClick={() => setSelectedAnnotation(annotation)}
                >
                  查看
                </Button>,
                annotation.status === 'open' && !readonly && (
                  <Button
                    type="text"
                    size="small"
                    onClick={() => onResolveAnnotation(annotation.id)}
                  >
                    解决
                  </Button>
                ),
                !readonly && annotation.authorId === currentUserId && (
                  <Button
                    type="text"
                    size="small"
                    danger
                    onClick={() => onDeleteAnnotation(annotation.id)}
                  >
                    删除
                  </Button>
                ),
              ].filter(Boolean)}
            >
              <List.Item.Meta
                avatar={
                  <Badge
                    dot={annotation.status === 'open'}
                    color={getTypeColor(annotation.type)}
                  >
                    <Avatar icon={getTypeIcon(annotation.type)} />
                  </Badge>
                }
                title={
                  <Space>
                    <span>{annotation.title}</span>
                    <Tag color={getTypeColor(annotation.type)} size="small">
                      {getTypeText(annotation.type)}
                    </Tag>
                    <Tag color={getPriorityColor(annotation.priority)} size="small">
                      {getPriorityText(annotation.priority)}
                    </Tag>
                  </Space>
                }
                description={
                  <div>
                    <Text>{annotation.content}</Text>
                    <br />
                    <Space style={{ marginTop: 4 }}>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {annotation.authorName}
                      </Text>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {new Date(annotation.createdAt).toLocaleString('zh-CN')}
                      </Text>
                      {annotation.responses.length > 0 && (
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {annotation.responses.length} 回复
                        </Text>
                      )}
                    </Space>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Card>

      {/* Create Annotation Modal */}
      <Modal
        title="添加注释"
        open={isCreating}
        onCancel={() => setIsCreating(false)}
        onOk={handleCreateAnnotation}
        okText="添加"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item label="类型" required>
            <Select
              value={newAnnotation.type}
              onChange={(value) => setNewAnnotation(prev => ({ ...prev, type: value }))}
            >
              <Option value={AnnotationType.COMMENT}>
                <Space>
                  <CommentOutlined />
                  评论
                </Space>
              </Option>
              <Option value={AnnotationType.SUGGESTION}>
                <Space>
                  <BulbOutlined />
                  建议
                </Space>
              </Option>
              <Option value={AnnotationType.ISSUE}>
                <Space>
                  <ExclamationCircleOutlined />
                  问题
                </Space>
              </Option>
              <Option value={AnnotationType.QUESTION}>
                <Space>
                  <QuestionCircleOutlined />
                  疑问
                </Space>
              </Option>
            </Select>
          </Form.Item>

          <Form.Item label="优先级" required>
            <Select
              value={newAnnotation.priority}
              onChange={(value) => setNewAnnotation(prev => ({ ...prev, priority: value }))}
            >
              <Option value="low">
                <Tag color="green">低</Tag>
              </Option>
              <Option value="medium">
                <Tag color="orange">中</Tag>
              </Option>
              <Option value="high">
                <Tag color="red">高</Tag>
              </Option>
            </Select>
          </Form.Item>

          <Form.Item label="标题" required>
            <Input
              value={newAnnotation.title}
              onChange={(e) => setNewAnnotation(prev => ({ ...prev, title: e.target.value }))}
              placeholder="请输入注释标题"
            />
          </Form.Item>

          <Form.Item label="内容" required>
            <TextArea
              value={newAnnotation.content}
              onChange={(e) => setNewAnnotation(prev => ({ ...prev, content: e.target.value }))}
              placeholder="请输入注释内容"
              rows={4}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Annotation Detail Modal */}
      <Modal
        title="注释详情"
        open={!!selectedAnnotation}
        onCancel={() => setSelectedAnnotation(null)}
        footer={null}
        width={600}
      >
        {selectedAnnotation && (
          <div>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Space>
                  <Tag color={getTypeColor(selectedAnnotation.type)} icon={getTypeIcon(selectedAnnotation.type)}>
                    {getTypeText(selectedAnnotation.type)}
                  </Tag>
                  <Tag color={getPriorityColor(selectedAnnotation.priority)}>
                    {getPriorityText(selectedAnnotation.priority)}
                  </Tag>
                  <Tag color={selectedAnnotation.status === 'open' ? 'orange' : 'green'}>
                    {selectedAnnotation.status === 'open' ? '未解决' : '已解决'}
                  </Tag>
                </Space>
              </div>

              <div>
                <Title level={5}>{selectedAnnotation.title}</Title>
                <Text>{selectedAnnotation.content}</Text>
              </div>

              <div>
                <Space>
                  <Avatar />
                  <div>
                    <Text strong>{selectedAnnotation.authorName}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {new Date(selectedAnnotation.createdAt).toLocaleString('zh-CN')}
                    </Text>
                  </div>
                </Space>
              </div>

              <Divider />

              <div>
                <Title level={5}>回复 ({selectedAnnotation.responses.length})</Title>
                <List
                  dataSource={selectedAnnotation.responses}
                  renderItem={(response) => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={<Avatar />}
                        title={
                          <Space>
                            <span>{response.authorName}</span>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              {new Date(response.createdAt).toLocaleString('zh-CN')}
                            </Text>
                          </Space>
                        }
                        description={response.content}
                      />
                    </List.Item>
                  )}
                />

                {!readonly && selectedAnnotation.status === 'open' && (
                  <div style={{ marginTop: 16 }}>
                    <TextArea
                      value={replyText}
                      onChange={(e) => setReplyText(e.target.value)}
                      placeholder="添加回复..."
                      rows={3}
                    />
                    <div style={{ marginTop: 8, textAlign: 'right' }}>
                      <Button
                        type="primary"
                        onClick={() => handleAddReply(selectedAnnotation.id)}
                        disabled={!replyText.trim()}
                      >
                        回复
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </Space>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default AnnotationTool
