import React, { useState } from 'react'
import {
  Card,
  Row,
  Col,
  Select,
  Button,
  Space,
  Typography,
  Tag,
  Table,
  Collapse,
  Alert,
  Statistic,
  Progress,
  Divider,
  Timeline,
  Tooltip,
} from 'antd'
import {
  SwapOutlined,
  PlusOutlined,
  MinusOutlined,
  EditOutlined,
  <PERSON>RightOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  DollarOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons'
import { VersionComparison, VersionDifference, ComparisonSummary } from '../../types/customerReview'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography
const { Option } = Select
const { Panel } = Collapse

interface VersionComparisonProps {
  versions: string[]
  onCompare: (baseVersion: string, compareVersion: string) => void
  comparison?: VersionComparison
  loading?: boolean
}

// Mock comparison data
const mockComparison: VersionComparison = {
  id: 'comparison_1',
  solutionId: 'solution_1',
  baseVersion: '1.0',
  compareVersion: '1.1',
  differences: [
    {
      id: 'diff_1',
      type: 'added',
      category: 'component',
      path: 'components.graphql_api',
      description: '添加GraphQL API组件',
      newValue: {
        name: 'GraphQL API',
        cost: 12000,
        duration: 15,
      },
      impact: 'medium',
      costImpact: 12000,
      timeImpact: 15,
    },
    {
      id: 'diff_2',
      type: 'modified',
      category: 'configuration',
      path: 'database.config.max_connections',
      description: '数据库最大连接数配置调整',
      oldValue: 100,
      newValue: 200,
      impact: 'low',
      costImpact: 0,
      timeImpact: 0,
    },
    {
      id: 'diff_3',
      type: 'modified',
      category: 'cost',
      path: 'cost.customization',
      description: '定制开发成本调整',
      oldValue: 30000,
      newValue: 36000,
      impact: 'medium',
      costImpact: 6000,
      timeImpact: 5,
    },
    {
      id: 'diff_4',
      type: 'added',
      category: 'feature',
      path: 'features.real_time_notifications',
      description: '新增实时通知功能',
      newValue: {
        name: '实时通知',
        description: '支持WebSocket实时推送',
      },
      impact: 'high',
      costImpact: 8000,
      timeImpact: 10,
    },
  ],
  summary: {
    totalChanges: 4,
    addedItems: 2,
    removedItems: 0,
    modifiedItems: 2,
    costDifference: 26000,
    timeDifference: 30,
    impactLevel: 'medium',
    recommendations: [
      '新增的GraphQL API将提高数据查询效率',
      '实时通知功能将改善用户体验',
      '建议评估成本增加对预算的影响',
    ],
  },
  createdAt: '2024-01-15T15:00:00Z',
}

const VersionComparison: React.FC<VersionComparisonProps> = ({
  versions = ['1.0', '1.1', '1.2'],
  onCompare,
  comparison = mockComparison,
  loading = false,
}) => {
  const [baseVersion, setBaseVersion] = useState('1.0')
  const [compareVersion, setCompareVersion] = useState('1.1')

  const handleCompare = () => {
    onCompare(baseVersion, compareVersion)
  }

  const getChangeTypeIcon = (type: string) => {
    const icons = {
      added: <PlusOutlined style={{ color: '#52c41a' }} />,
      removed: <MinusOutlined style={{ color: '#ff4d4f' }} />,
      modified: <EditOutlined style={{ color: '#faad14' }} />,
      moved: <SwapOutlined style={{ color: '#1890ff' }} />,
    }
    return icons[type as keyof typeof icons]
  }

  const getChangeTypeColor = (type: string) => {
    const colors = {
      added: 'success',
      removed: 'error',
      modified: 'warning',
      moved: 'processing',
    }
    return colors[type as keyof typeof colors]
  }

  const getChangeTypeText = (type: string) => {
    const texts = {
      added: '新增',
      removed: '删除',
      modified: '修改',
      moved: '移动',
    }
    return texts[type as keyof typeof texts]
  }

  const getImpactColor = (impact: string) => {
    const colors = {
      low: 'green',
      medium: 'orange',
      high: 'red',
    }
    return colors[impact as keyof typeof colors]
  }

  const getImpactText = (impact: string) => {
    const texts = {
      low: '低',
      medium: '中',
      high: '高',
    }
    return texts[impact as keyof typeof texts]
  }

  const getCategoryIcon = (category: string) => {
    const icons = {
      component: '📦',
      configuration: '⚙️',
      cost: '💰',
      timeline: '⏰',
      feature: '✨',
    }
    return icons[category as keyof typeof icons]
  }

  const formatCurrency = (amount: number) => {
    return `¥${amount.toLocaleString()}`
  }

  const differenceColumns: ColumnsType<VersionDifference> = [
    {
      title: '变更类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type) => (
        <Space>
          {getChangeTypeIcon(type)}
          <Tag color={getChangeTypeColor(type)}>
            {getChangeTypeText(type)}
          </Tag>
        </Space>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category) => (
        <Space>
          <span>{getCategoryIcon(category)}</span>
          <span>{category}</span>
        </Space>
      ),
    },
    {
      title: '变更描述',
      dataIndex: 'description',
      key: 'description',
      render: (description, record) => (
        <div>
          <Text strong>{description}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.path}
          </Text>
        </div>
      ),
    },
    {
      title: '变更内容',
      key: 'changes',
      render: (_, record) => (
        <div>
          {record.type === 'added' && (
            <div>
              <Text type="success">+ {JSON.stringify(record.newValue)}</Text>
            </div>
          )}
          {record.type === 'removed' && (
            <div>
              <Text type="danger">- {JSON.stringify(record.oldValue)}</Text>
            </div>
          )}
          {record.type === 'modified' && (
            <div>
              <Text type="danger">- {JSON.stringify(record.oldValue)}</Text>
              <br />
              <Text type="success">+ {JSON.stringify(record.newValue)}</Text>
            </div>
          )}
        </div>
      ),
    },
    {
      title: '影响程度',
      dataIndex: 'impact',
      key: 'impact',
      width: 80,
      render: (impact) => (
        <Tag color={getImpactColor(impact)}>
          {getImpactText(impact)}
        </Tag>
      ),
    },
    {
      title: '成本影响',
      dataIndex: 'costImpact',
      key: 'costImpact',
      width: 100,
      render: (cost) => (
        <Text style={{ color: cost > 0 ? '#ff4d4f' : cost < 0 ? '#52c41a' : '#666' }}>
          {cost > 0 ? '+' : ''}{formatCurrency(cost)}
        </Text>
      ),
    },
    {
      title: '时间影响',
      dataIndex: 'timeImpact',
      key: 'timeImpact',
      width: 100,
      render: (time) => (
        <Text style={{ color: time > 0 ? '#ff4d4f' : time < 0 ? '#52c41a' : '#666' }}>
          {time > 0 ? '+' : ''}{time}天
        </Text>
      ),
    },
  ]

  const renderSummary = (summary: ComparisonSummary) => (
    <Row gutter={[16, 16]}>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="总变更数"
            value={summary.totalChanges}
            suffix="项"
            prefix={<SwapOutlined />}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="成本变化"
            value={summary.costDifference}
            formatter={(value) => formatCurrency(Number(value))}
            prefix={<DollarOutlined />}
            valueStyle={{ color: summary.costDifference > 0 ? '#ff4d4f' : '#52c41a' }}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="时间变化"
            value={summary.timeDifference}
            suffix="天"
            prefix={<ClockCircleOutlined />}
            valueStyle={{ color: summary.timeDifference > 0 ? '#ff4d4f' : '#52c41a' }}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="影响程度"
            value={getImpactText(summary.impactLevel)}
            prefix={<WarningOutlined />}
            valueStyle={{ color: getImpactColor(summary.impactLevel) }}
          />
        </Card>
      </Col>
    </Row>
  )

  const renderChangeDistribution = (summary: ComparisonSummary) => (
    <Card title="变更分布" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
            <Text>新增项目</Text>
            <Text strong>{summary.addedItems}</Text>
          </div>
          <Progress
            percent={(summary.addedItems / summary.totalChanges) * 100}
            strokeColor="#52c41a"
            showInfo={false}
          />
        </div>

        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
            <Text>修改项目</Text>
            <Text strong>{summary.modifiedItems}</Text>
          </div>
          <Progress
            percent={(summary.modifiedItems / summary.totalChanges) * 100}
            strokeColor="#faad14"
            showInfo={false}
          />
        </div>

        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
            <Text>删除项目</Text>
            <Text strong>{summary.removedItems}</Text>
          </div>
          <Progress
            percent={(summary.removedItems / summary.totalChanges) * 100}
            strokeColor="#ff4d4f"
            showInfo={false}
          />
        </div>
      </Space>
    </Card>
  )

  return (
    <div>
      {/* Version Selection */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col>
            <Text strong>基准版本:</Text>
          </Col>
          <Col>
            <Select
              value={baseVersion}
              onChange={setBaseVersion}
              style={{ width: 120 }}
            >
              {versions.map(version => (
                <Option key={version} value={version}>
                  v{version}
                </Option>
              ))}
            </Select>
          </Col>
          <Col>
            <ArrowRightOutlined style={{ color: '#666' }} />
          </Col>
          <Col>
            <Text strong>对比版本:</Text>
          </Col>
          <Col>
            <Select
              value={compareVersion}
              onChange={setCompareVersion}
              style={{ width: 120 }}
            >
              {versions.map(version => (
                <Option key={version} value={version}>
                  v{version}
                </Option>
              ))}
            </Select>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<SwapOutlined />}
              onClick={handleCompare}
              loading={loading}
              disabled={baseVersion === compareVersion}
            >
              开始对比
            </Button>
          </Col>
        </Row>
      </Card>

      {comparison && (
        <>
          {/* Summary */}
          <Card title="对比摘要" style={{ marginBottom: 16 }}>
            {renderSummary(comparison.summary)}
          </Card>

          {/* Recommendations */}
          {comparison.summary.recommendations.length > 0 && (
            <Alert
              message="建议"
              description={
                <ul style={{ margin: 0, paddingLeft: 20 }}>
                  {comparison.summary.recommendations.map((rec, index) => (
                    <li key={index}>{rec}</li>
                  ))}
                </ul>
              }
              type="info"
              icon={<InfoCircleOutlined />}
              style={{ marginBottom: 16 }}
            />
          )}

          {/* Details */}
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={18}>
              <Card title="详细变更" size="small">
                <Table
                  columns={differenceColumns}
                  dataSource={comparison.differences}
                  rowKey="id"
                  pagination={false}
                  size="small"
                />
              </Card>
            </Col>

            <Col xs={24} lg={6}>
              {renderChangeDistribution(comparison.summary)}

              <Card title="版本时间线" size="small" style={{ marginTop: 16 }}>
                <Timeline size="small">
                  <Timeline.Item color="green">
                    <Text strong>v{comparison.baseVersion}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      基准版本
                    </Text>
                  </Timeline.Item>
                  <Timeline.Item color="blue">
                    <Text strong>v{comparison.compareVersion}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      对比版本
                    </Text>
                  </Timeline.Item>
                </Timeline>
              </Card>
            </Col>
          </Row>

          {/* Detailed Changes by Category */}
          <Card title="分类变更详情" style={{ marginTop: 16 }}>
            <Collapse>
              {['component', 'configuration', 'cost', 'feature'].map(category => {
                const categoryDiffs = comparison.differences.filter(d => d.category === category)
                if (categoryDiffs.length === 0) return null

                return (
                  <Panel
                    header={
                      <Space>
                        <span>{getCategoryIcon(category)}</span>
                        <span>{category}</span>
                        <Tag>{categoryDiffs.length}</Tag>
                      </Space>
                    }
                    key={category}
                  >
                    <Table
                      columns={differenceColumns.filter(col => col.key !== 'category')}
                      dataSource={categoryDiffs}
                      rowKey="id"
                      pagination={false}
                      size="small"
                    />
                  </Panel>
                )
              })}
            </Collapse>
          </Card>
        </>
      )}
    </div>
  )
}

export default VersionComparison
