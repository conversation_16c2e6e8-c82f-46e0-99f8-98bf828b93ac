import React, { useState, useRef } from 'react'
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Typography,
  Tag,
  Tabs,
  Timeline,
  Progress,
  Statistic,
  Tooltip,
  Modal,
  Rate,
  Divider,
  List,
  Avatar,
  Badge,
} from 'antd'
import {
  EyeOutlined,
  FullscreenOutlined,
  DownloadOutlined,
  ShareAltOutlined,
  CommentOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  EditOutlined,
  HistoryOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  FileTextOutlined,
} from '@ant-design/icons'
import { CustomSolution } from '../../types/solutions'
import { SolutionReview, ReviewStatus, VisualizationMode } from '../../types/customerReview'

const { Title, Text, Paragraph } = Typography
const { TabPane } = Tabs

interface SolutionPreviewProps {
  solution: CustomSolution
  review?: SolutionReview
  onApprove: () => void
  onReject: (reason: string) => void
  onRequestRevision: (changes: any[]) => void
  onAddAnnotation: (annotation: any) => void
  readonly?: boolean
}

// Mock review data
const mockReview: SolutionReview = {
  id: 'review_1',
  solutionId: 'solution_1',
  solutionName: '企业管理系统定制方案',
  solutionVersion: '1.0',
  customerId: '2',
  customerName: '张三',
  status: ReviewStatus.IN_REVIEW,
  submittedAt: '2024-01-15T10:00:00Z',
  reviewDeadline: '2024-01-20T18:00:00Z',
  currentStep: 2,
  totalSteps: 4,
  annotations: [],
  approvals: [],
  revisions: [],
  attachments: [],
  metadata: {},
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T14:30:00Z',
}

const SolutionPreview: React.FC<SolutionPreviewProps> = ({
  solution,
  review = mockReview,
  onApprove,
  onReject,
  onRequestRevision,
  onAddAnnotation,
  readonly = false,
}) => {
  const [activeTab, setActiveTab] = useState('overview')
  const [visualizationMode, setVisualizationMode] = useState<VisualizationMode>(VisualizationMode.OVERVIEW)
  const [fullscreenVisible, setFullscreenVisible] = useState(false)
  const [annotationMode, setAnnotationMode] = useState(false)
  const canvasRef = useRef<HTMLDivElement>(null)

  const getStatusColor = (status: ReviewStatus) => {
    const colors = {
      [ReviewStatus.PENDING]: 'default',
      [ReviewStatus.IN_REVIEW]: 'processing',
      [ReviewStatus.APPROVED]: 'success',
      [ReviewStatus.REJECTED]: 'error',
      [ReviewStatus.REVISION_REQUESTED]: 'warning',
      [ReviewStatus.EXPIRED]: 'error',
    }
    return colors[status]
  }

  const getStatusText = (status: ReviewStatus) => {
    const texts = {
      [ReviewStatus.PENDING]: '待审核',
      [ReviewStatus.IN_REVIEW]: '审核中',
      [ReviewStatus.APPROVED]: '已批准',
      [ReviewStatus.REJECTED]: '已拒绝',
      [ReviewStatus.REVISION_REQUESTED]: '需要修改',
      [ReviewStatus.EXPIRED]: '已过期',
    }
    return texts[status]
  }

  const getComplexityColor = (complexity: string) => {
    const colors = {
      low: 'green',
      medium: 'orange',
      high: 'red',
    }
    return colors[complexity as keyof typeof colors]
  }

  const formatCurrency = (amount: number) => {
    return `¥${amount.toLocaleString()}`
  }

  const calculateProgress = () => {
    return Math.round((review.currentStep / review.totalSteps) * 100)
  }

  const getDaysRemaining = () => {
    if (!review.reviewDeadline) return null
    const deadline = new Date(review.reviewDeadline)
    const now = new Date()
    const diffTime = deadline.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const renderVisualization = () => {
    return (
      <div
        ref={canvasRef}
        style={{
          width: '100%',
          height: 400,
          backgroundColor: '#f5f5f5',
          border: '1px solid #d9d9d9',
          borderRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
          backgroundImage: 'radial-gradient(circle, #ccc 1px, transparent 1px)',
          backgroundSize: '20px 20px',
        }}
      >
        {/* Mock 3D Visualization */}
        <div style={{ textAlign: 'center' }}>
          <div
            style={{
              width: 200,
              height: 150,
              backgroundColor: '#1890ff',
              borderRadius: 8,
              margin: '0 auto 16px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: 48,
              boxShadow: '0 4px 12px rgba(24, 144, 255, 0.3)',
            }}
          >
            🏢
          </div>
          <Text strong>企业管理系统架构</Text>
          <br />
          <Text type="secondary">3D可视化预览</Text>
        </div>

        {/* Visualization Controls */}
        <div
          style={{
            position: 'absolute',
            top: 16,
            right: 16,
            display: 'flex',
            flexDirection: 'column',
            gap: 8,
          }}
        >
          <Button
            type="primary"
            icon={<FullscreenOutlined />}
            onClick={() => setFullscreenVisible(true)}
          />
          <Button
            icon={<CommentOutlined />}
            onClick={() => setAnnotationMode(!annotationMode)}
            style={{
              backgroundColor: annotationMode ? '#52c41a' : undefined,
              borderColor: annotationMode ? '#52c41a' : undefined,
              color: annotationMode ? 'white' : undefined,
            }}
          />
        </div>

        {/* Annotation Overlay */}
        {annotationMode && (
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
              cursor: 'crosshair',
            }}
            onClick={(e) => {
              const rect = canvasRef.current?.getBoundingClientRect()
              if (rect) {
                const x = e.clientX - rect.left
                const y = e.clientY - rect.top
                onAddAnnotation({
                  position: { x, y, viewMode: visualizationMode },
                  type: 'comment',
                })
              }
            }}
          />
        )}
      </div>
    )
  }

  const renderTimeline = () => {
    const timelineItems = [
      {
        color: 'green',
        children: (
          <div>
            <Text strong>需求确认</Text>
            <br />
            <Text type="secondary">2024-01-15 10:00</Text>
          </div>
        ),
      },
      {
        color: 'blue',
        children: (
          <div>
            <Text strong>方案设计</Text>
            <br />
            <Text type="secondary">2024-01-15 14:30</Text>
          </div>
        ),
      },
      {
        color: 'orange',
        dot: <ClockCircleOutlined />,
        children: (
          <div>
            <Text strong>客户审核</Text>
            <br />
            <Text type="secondary">进行中</Text>
          </div>
        ),
      },
      {
        color: 'gray',
        children: (
          <div>
            <Text strong>最终确认</Text>
            <br />
            <Text type="secondary">待进行</Text>
          </div>
        ),
      },
    ]

    return <Timeline items={timelineItems} />
  }

  const renderComponents = () => {
    const mockComponents = [
      { name: 'React前端框架', type: '前端', status: '已确认', cost: 15000 },
      { name: 'Node.js后端服务', type: '后端', status: '已确认', cost: 25000 },
      { name: 'PostgreSQL数据库', type: '数据库', status: '已确认', cost: 8000 },
      { name: 'GraphQL API', type: 'API', status: '待确认', cost: 12000 },
    ]

    return (
      <List
        dataSource={mockComponents}
        renderItem={(item) => (
          <List.Item
            actions={[
              <Tag color={item.status === '已确认' ? 'green' : 'orange'}>
                {item.status}
              </Tag>,
              <Text>{formatCurrency(item.cost)}</Text>,
            ]}
          >
            <List.Item.Meta
              avatar={<Avatar style={{ backgroundColor: '#1890ff' }}>📦</Avatar>}
              title={item.name}
              description={`类型: ${item.type}`}
            />
          </List.Item>
        )}
      />
    )
  }

  const daysRemaining = getDaysRemaining()

  return (
    <div>
      {/* Header */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space direction="vertical" size="small">
              <Title level={3} style={{ margin: 0 }}>
                {solution.name}
              </Title>
              <Space>
                <Tag color={getStatusColor(review.status)}>
                  {getStatusText(review.status)}
                </Tag>
                <Tag color={getComplexityColor(solution.complexity)}>
                  {solution.complexity === 'low' ? '简单' :
                   solution.complexity === 'medium' ? '中等' : '复杂'}
                </Tag>
                <Text type="secondary">版本 {solution.version}</Text>
              </Space>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button icon={<DownloadOutlined />}>
                下载方案
              </Button>
              <Button icon={<ShareAltOutlined />}>
                分享
              </Button>
              {!readonly && review.status === ReviewStatus.IN_REVIEW && (
                <>
                  <Button
                    type="primary"
                    icon={<CheckCircleOutlined />}
                    onClick={onApprove}
                  >
                    批准
                  </Button>
                  <Button
                    danger
                    icon={<CloseCircleOutlined />}
                    onClick={() => onReject('客户拒绝')}
                  >
                    拒绝
                  </Button>
                  <Button
                    icon={<EditOutlined />}
                    onClick={() => onRequestRevision([])}
                  >
                    请求修改
                  </Button>
                </>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Progress and Stats */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="审核进度"
              value={calculateProgress()}
              suffix="%"
              prefix={<Progress type="circle" percent={calculateProgress()} size={40} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="预估成本"
              value={solution.estimatedCost}
              formatter={(value) => formatCurrency(Number(value))}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="预估工期"
              value={solution.estimatedDuration}
              suffix="天"
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="剩余时间"
              value={daysRemaining || 0}
              suffix="天"
              prefix={<HistoryOutlined />}
              valueStyle={{ color: daysRemaining && daysRemaining < 3 ? '#ff4d4f' : '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="方案概览" key="overview">
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={16}>
                <Card title="解决方案可视化" size="small">
                  <Space style={{ marginBottom: 16 }}>
                    <Button
                      type={visualizationMode === VisualizationMode.OVERVIEW ? 'primary' : 'default'}
                      onClick={() => setVisualizationMode(VisualizationMode.OVERVIEW)}
                    >
                      概览
                    </Button>
                    <Button
                      type={visualizationMode === VisualizationMode.DETAILED ? 'primary' : 'default'}
                      onClick={() => setVisualizationMode(VisualizationMode.DETAILED)}
                    >
                      详细
                    </Button>
                    <Button
                      type={visualizationMode === VisualizationMode.INTERACTIVE_3D ? 'primary' : 'default'}
                      onClick={() => setVisualizationMode(VisualizationMode.INTERACTIVE_3D)}
                    >
                      3D交互
                    </Button>
                    <Button
                      type={visualizationMode === VisualizationMode.ARCHITECTURE ? 'primary' : 'default'}
                      onClick={() => setVisualizationMode(VisualizationMode.ARCHITECTURE)}
                    >
                      架构图
                    </Button>
                  </Space>
                  {renderVisualization()}
                </Card>

                <Card title="方案描述" size="small" style={{ marginTop: 16 }}>
                  <Paragraph>{solution.description}</Paragraph>
                  
                  <Title level={5}>主要功能</Title>
                  <List
                    size="small"
                    dataSource={['用户管理', '权限控制', '数据分析', '报表生成', '工作流管理']}
                    renderItem={item => <List.Item>• {item}</List.Item>}
                  />

                  <Title level={5}>技术特点</Title>
                  <Space wrap>
                    {['React', 'Node.js', 'PostgreSQL', 'TypeScript', 'Ant Design'].map(tech => (
                      <Tag key={tech} color="blue">{tech}</Tag>
                    ))}
                  </Space>
                </Card>
              </Col>

              <Col xs={24} lg={8}>
                <Card title="审核时间线" size="small">
                  {renderTimeline()}
                </Card>

                <Card title="关键指标" size="small" style={{ marginTop: 16 }}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>功能完整度</Text>
                      <Text strong>95%</Text>
                    </div>
                    <Progress percent={95} size="small" />

                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>技术成熟度</Text>
                      <Text strong>88%</Text>
                    </div>
                    <Progress percent={88} size="small" />

                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>可维护性</Text>
                      <Text strong>92%</Text>
                    </div>
                    <Progress percent={92} size="small" />

                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>扩展性</Text>
                      <Text strong>85%</Text>
                    </div>
                    <Progress percent={85} size="small" />
                  </Space>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="组件详情" key="components">
            <Card title="解决方案组件" size="small">
              {renderComponents()}
            </Card>
          </TabPane>

          <TabPane tab="成本分析" key="cost">
            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Card title="成本构成" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>基础开发</Text>
                      <Text strong>{formatCurrency(120000)}</Text>
                    </div>
                    <Progress percent={67} strokeColor="#1890ff" />

                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>定制开发</Text>
                      <Text strong>{formatCurrency(36000)}</Text>
                    </div>
                    <Progress percent={20} strokeColor="#52c41a" />

                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>测试部署</Text>
                      <Text strong>{formatCurrency(18000)}</Text>
                    </div>
                    <Progress percent={10} strokeColor="#faad14" />

                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>培训支持</Text>
                      <Text strong>{formatCurrency(6000)}</Text>
                    </div>
                    <Progress percent={3} strokeColor="#722ed1" />
                  </Space>
                </Card>
              </Col>

              <Col xs={24} md={12}>
                <Card title="时间安排" size="small">
                  <Timeline
                    items={[
                      {
                        color: 'green',
                        children: '需求分析 (5天)',
                      },
                      {
                        color: 'blue',
                        children: '系统设计 (10天)',
                      },
                      {
                        color: 'orange',
                        children: '开发实施 (60天)',
                      },
                      {
                        color: 'purple',
                        children: '测试验收 (15天)',
                      },
                      {
                        color: 'cyan',
                        children: '部署上线 (5天)',
                      },
                    ]}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="版本历史" key="versions">
            <Card title="版本变更记录" size="small">
              <Timeline>
                <Timeline.Item color="green">
                  <Text strong>v1.0</Text> - 初始版本
                  <br />
                  <Text type="secondary">2024-01-15 10:00</Text>
                  <br />
                  <Text>创建基础解决方案架构</Text>
                </Timeline.Item>
                <Timeline.Item color="blue">
                  <Text strong>v1.1</Text> - 功能优化
                  <br />
                  <Text type="secondary">2024-01-15 14:30</Text>
                  <br />
                  <Text>添加GraphQL API支持，优化数据库设计</Text>
                </Timeline.Item>
              </Timeline>
            </Card>
          </TabPane>
        </Tabs>
      </Card>

      {/* Fullscreen Modal */}
      <Modal
        title="3D可视化 - 全屏模式"
        open={fullscreenVisible}
        onCancel={() => setFullscreenVisible(false)}
        width="95%"
        style={{ top: 20 }}
        footer={null}
      >
        <div style={{ height: '70vh' }}>
          {renderVisualization()}
        </div>
      </Modal>
    </div>
  )
}

export default SolutionPreview
