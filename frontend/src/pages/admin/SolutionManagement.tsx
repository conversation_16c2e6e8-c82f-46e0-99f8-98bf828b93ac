import React, { useState } from 'react'
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Modal,
  message,
  Drawer,
  Steps,
} from 'antd'
import {
  PlusOutlined,
  AppstoreOutlined,
  DesktopOutlined,
  CalculatorOutlined,
  FileTextOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
} from '@ant-design/icons'
import { SolutionTemplate, CustomSolution, SolutionStatus, CostBreakdown, TimelineEstimate } from '../../types/solutions'
import SolutionLibrary from '../../components/solutions/SolutionLibrary'
import SolutionDesigner from '../../components/solutions/SolutionDesigner'
import CostCalculator from '../../components/solutions/CostCalculator'
import WorkflowManager from '../../components/workflow/WorkflowManager'
import type { ColumnsType } from 'antd/es/table'

const { Title } = Typography
const { TabPane } = Tabs
const { Step } = Steps

// Mock custom solutions
const mockCustomSolutions: CustomSolution[] = [
  {
    id: 'solution_1',
    name: '企业管理系统定制方案',
    description: '基于企业管理系统模板的定制化解决方案',
    requirementId: 'req_1',
    templateId: 'template_1',
    components: [],
    customComponents: [],
    configuration: {},
    estimatedCost: 180000,
    estimatedDuration: 95,
    complexity: 'high',
    status: SolutionStatus.UNDER_REVIEW,
    version: '1.0',
    changelog: [],
    approvals: [],
    comments: [],
    attachments: [],
    tags: ['ERP', '定制开发'],
    metadata: {},
    createdBy: '1',
    assignedTo: '4',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z',
  },
  {
    id: 'solution_2',
    name: '电商平台移动端方案',
    description: '电商平台的移动端扩展解决方案',
    requirementId: 'req_2',
    templateId: 'template_2',
    components: [],
    customComponents: [],
    configuration: {},
    estimatedCost: 120000,
    estimatedDuration: 60,
    complexity: 'medium',
    status: SolutionStatus.APPROVED,
    version: '1.2',
    changelog: [],
    approvals: [],
    comments: [],
    attachments: [],
    tags: ['电商', '移动端'],
    metadata: {},
    createdBy: '3',
    assignedTo: '4',
    createdAt: '2024-01-14T09:00:00Z',
    updatedAt: '2024-01-15T11:20:00Z',
  },
]

const SolutionManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('library')
  const [customSolutions, setCustomSolutions] = useState<CustomSolution[]>(mockCustomSolutions)
  const [selectedSolution, setSelectedSolution] = useState<CustomSolution | null>(null)
  const [designerVisible, setDesignerVisible] = useState(false)
  const [calculatorVisible, setCalculatorVisible] = useState(false)
  const [workflowVisible, setWorkflowVisible] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)

  const handleSelectTemplate = (template: SolutionTemplate) => {
    // Create new solution from template
    const newSolution: CustomSolution = {
      id: `solution_${Date.now()}`,
      name: `${template.name} - 定制方案`,
      description: `基于${template.name}的定制化解决方案`,
      requirementId: '',
      templateId: template.id,
      components: [],
      customComponents: [],
      configuration: {},
      estimatedCost: template.baseCost,
      estimatedDuration: template.estimatedDuration,
      complexity: template.complexity,
      status: SolutionStatus.DRAFT,
      version: '1.0',
      changelog: [],
      approvals: [],
      comments: [],
      attachments: [],
      tags: template.tags,
      metadata: {},
      createdBy: '1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    setCustomSolutions(prev => [newSolution, ...prev])
    setSelectedSolution(newSolution)
    setDesignerVisible(true)
    setCurrentStep(1)
    message.success(`已创建基于 ${template.name} 的解决方案`)
  }

  const handleSaveDesign = (components: any[], connections: any[]) => {
    if (!selectedSolution) return

    const updatedSolution = {
      ...selectedSolution,
      components,
      updatedAt: new Date().toISOString(),
    }

    setCustomSolutions(prev =>
      prev.map(s => s.id === selectedSolution.id ? updatedSolution : s)
    )
    setSelectedSolution(updatedSolution)
    setCurrentStep(2)
    message.success('设计已保存')
  }

  const handleCostChange = (breakdown: CostBreakdown) => {
    if (!selectedSolution) return

    const updatedSolution = {
      ...selectedSolution,
      estimatedCost: breakdown.total,
      updatedAt: new Date().toISOString(),
    }

    setCustomSolutions(prev =>
      prev.map(s => s.id === selectedSolution.id ? updatedSolution : s)
    )
    setSelectedSolution(updatedSolution)
  }

  const handleTimelineChange = (timeline: TimelineEstimate) => {
    if (!selectedSolution) return

    const updatedSolution = {
      ...selectedSolution,
      estimatedDuration: timeline.totalDuration,
      updatedAt: new Date().toISOString(),
    }

    setCustomSolutions(prev =>
      prev.map(s => s.id === selectedSolution.id ? updatedSolution : s)
    )
    setSelectedSolution(updatedSolution)
  }

  const handleSubmitForApproval = (solutionId: string) => {
    setCustomSolutions(prev =>
      prev.map(s =>
        s.id === solutionId
          ? { ...s, status: SolutionStatus.UNDER_REVIEW, updatedAt: new Date().toISOString() }
          : s
      )
    )
    setCurrentStep(3)
    message.success('解决方案已提交审批')
  }

  const handleApproveSolution = (solutionId: string) => {
    setCustomSolutions(prev =>
      prev.map(s =>
        s.id === solutionId
          ? { ...s, status: SolutionStatus.APPROVED, updatedAt: new Date().toISOString() }
          : s
      )
    )
    message.success('解决方案已批准')
  }

  const handleRejectSolution = (solutionId: string) => {
    setCustomSolutions(prev =>
      prev.map(s =>
        s.id === solutionId
          ? { ...s, status: SolutionStatus.REJECTED, updatedAt: new Date().toISOString() }
          : s
      )
    )
    message.success('解决方案已拒绝')
  }

  const getStatusColor = (status: SolutionStatus) => {
    const colors = {
      [SolutionStatus.DRAFT]: 'default',
      [SolutionStatus.UNDER_REVIEW]: 'processing',
      [SolutionStatus.APPROVED]: 'success',
      [SolutionStatus.REJECTED]: 'error',
      [SolutionStatus.ACTIVE]: 'success',
      [SolutionStatus.DEPRECATED]: 'warning',
    }
    return colors[status]
  }

  const getStatusText = (status: SolutionStatus) => {
    const texts = {
      [SolutionStatus.DRAFT]: '草稿',
      [SolutionStatus.UNDER_REVIEW]: '审核中',
      [SolutionStatus.APPROVED]: '已批准',
      [SolutionStatus.REJECTED]: '已拒绝',
      [SolutionStatus.ACTIVE]: '活跃',
      [SolutionStatus.DEPRECATED]: '已废弃',
    }
    return texts[status]
  }

  const solutionColumns: ColumnsType<CustomSolution> = [
    {
      title: '方案名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ fontSize: 12, color: '#666' }}>{record.description}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '预估成本',
      dataIndex: 'estimatedCost',
      key: 'estimatedCost',
      render: (cost) => `¥${cost.toLocaleString()}`,
      align: 'right',
    },
    {
      title: '预估工期',
      dataIndex: 'estimatedDuration',
      key: 'estimatedDuration',
      render: (duration) => `${duration}天`,
      align: 'center',
    },
    {
      title: '复杂度',
      dataIndex: 'complexity',
      key: 'complexity',
      render: (complexity) => (
        <Tag color={complexity === 'high' ? 'red' : complexity === 'medium' ? 'orange' : 'green'}>
          {complexity === 'high' ? '复杂' : complexity === 'medium' ? '中等' : '简单'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString('zh-CN'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedSolution(record)
              setWorkflowVisible(true)
            }}
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => {
              setSelectedSolution(record)
              setDesignerVisible(true)
            }}
          />
          <Button
            type="text"
            icon={<CalculatorOutlined />}
            onClick={() => {
              setSelectedSolution(record)
              setCalculatorVisible(true)
            }}
          />
          {record.status === SolutionStatus.DRAFT && (
            <Button
              type="text"
              icon={<CheckOutlined />}
              onClick={() => handleSubmitForApproval(record.id)}
            />
          )}
          {record.status === SolutionStatus.UNDER_REVIEW && (
            <>
              <Button
                type="text"
                icon={<CheckOutlined />}
                style={{ color: '#52c41a' }}
                onClick={() => handleApproveSolution(record.id)}
              />
              <Button
                type="text"
                icon={<CloseOutlined />}
                style={{ color: '#ff4d4f' }}
                onClick={() => handleRejectSolution(record.id)}
              />
            </>
          )}
        </Space>
      ),
    },
  ]

  const steps = [
    {
      title: '选择模板',
      description: '从解决方案库选择合适的模板',
      icon: <AppstoreOutlined />,
    },
    {
      title: '设计方案',
      description: '使用设计器定制解决方案',
      icon: <DesktopOutlined />,
    },
    {
      title: '成本计算',
      description: '计算项目成本和时间线',
      icon: <CalculatorOutlined />,
    },
    {
      title: '审批流程',
      description: '提交审批并跟踪状态',
      icon: <FileTextOutlined />,
    },
  ]

  return (
    <div>
      {/* Header */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={2}>解决方案管理</Title>
        </Col>
        <Col>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setActiveTab('library')}
          >
            创建解决方案
          </Button>
        </Col>
      </Row>

      {/* Process Steps */}
      <Card style={{ marginBottom: 16 }}>
        <Steps current={currentStep} items={steps} />
      </Card>

      {/* Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总方案数"
              value={customSolutions.length}
              suffix="个"
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="待审核"
              value={customSolutions.filter(s => s.status === SolutionStatus.UNDER_REVIEW).length}
              suffix="个"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已批准"
              value={customSolutions.filter(s => s.status === SolutionStatus.APPROVED).length}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均成本"
              value={customSolutions.length > 0 ?
                customSolutions.reduce((sum, s) => sum + s.estimatedCost, 0) / customSolutions.length : 0}
              formatter={(value) => `¥${Number(value).toLocaleString()}`}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="解决方案库" key="library">
            <SolutionLibrary
              templates={[]}
              onSelectTemplate={handleSelectTemplate}
              onPreviewTemplate={(template) => message.info(`预览: ${template.name}`)}
              onCloneTemplate={(template) => message.info(`克隆: ${template.name}`)}
            />
          </TabPane>

          <TabPane tab="我的方案" key="solutions">
            <Table
              columns={solutionColumns}
              dataSource={customSolutions}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* Solution Designer Drawer */}
      <Drawer
        title="解决方案设计器"
        placement="right"
        width="90%"
        open={designerVisible}
        onClose={() => setDesignerVisible(false)}
        extra={
          <Space>
            <Button onClick={() => setCalculatorVisible(true)}>
              成本计算
            </Button>
            <Button
              type="primary"
              onClick={() => {
                setDesignerVisible(false)
                setCalculatorVisible(true)
              }}
            >
              下一步
            </Button>
          </Space>
        }
      >
        {selectedSolution && (
          <SolutionDesigner
            initialComponents={selectedSolution.components}
            onSave={handleSaveDesign}
            onValidate={(components) => message.info('验证功能开发中')}
          />
        )}
      </Drawer>

      {/* Cost Calculator Modal */}
      <Modal
        title="成本计算器"
        open={calculatorVisible}
        onCancel={() => setCalculatorVisible(false)}
        width="90%"
        footer={[
          <Button key="back" onClick={() => setCalculatorVisible(false)}>
            关闭
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              if (selectedSolution) {
                handleSubmitForApproval(selectedSolution.id)
                setCalculatorVisible(false)
              }
            }}
          >
            提交审批
          </Button>,
        ]}
      >
        {selectedSolution && (
          <CostCalculator
            components={selectedSolution.components}
            onCostChange={handleCostChange}
            onTimelineChange={handleTimelineChange}
          />
        )}
      </Modal>

      {/* Workflow Manager Modal */}
      <Modal
        title="审批流程"
        open={workflowVisible}
        onCancel={() => setWorkflowVisible(false)}
        width="80%"
        footer={null}
      >
        {selectedSolution && (
          <WorkflowManager
            entityId={selectedSolution.id}
            entityType="solution"
            currentStatus={selectedSolution.status as any}
            steps={[]}
            actions={[]}
            onApprove={(stepId, comments) => message.info('批准功能开发中')}
            onReject={(stepId, comments) => message.info('拒绝功能开发中')}
            onRequestChanges={(stepId, comments) => message.info('请求修改功能开发中')}
            onSkip={(stepId, reason) => message.info('跳过功能开发中')}
            onReassign={(stepId, newAssigneeId) => message.info('重新分配功能开发中')}
          />
        )}
      </Modal>
    </div>
  )
}

export default SolutionManagement
