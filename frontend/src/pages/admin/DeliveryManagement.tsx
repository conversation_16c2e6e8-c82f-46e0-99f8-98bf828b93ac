import React, { useState } from 'react'
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Table,
  Tag,
  Progress,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  message,
  Statistic,
  Alert,
  Badge,
} from 'antd'
import {
  DeliveredProcedureOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  QualityTimeOutlined,
  PlusOutlined,
  EyeOutlined,
  EditOutlined,
  UploadOutlined,
} from '@ant-design/icons'
import { DeliveryPackage, DeliveryStatus, AcceptanceStatus } from '../../types/delivery'
import DeliveryChecklist from '../../components/delivery/DeliveryChecklist'
import FileUploadPreview from '../../components/delivery/FileUploadPreview'
import AcceptanceWorkflow from '../../components/delivery/AcceptanceWorkflow'
import QualityControl from '../../components/delivery/QualityControl'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography
const { TabPane } = Tabs
const { TextArea } = Input
const { Option } = Select

// Mock delivery packages data
const mockDeliveryPackages: DeliveryPackage[] = [
  {
    id: 'package_1',
    projectId: 'proj_1',
    projectName: '企业管理系统',
    name: '企业管理系统 v1.0',
    description: '包含用户管理、权限控制、数据分析等核心功能的完整系统',
    version: '1.0',
    status: DeliveryStatus.IN_DELIVERY,
    deliverables: [],
    checklist: [],
    qualityReport: {} as any,
    acceptanceTests: [],
    deliveryDate: '2024-01-25T18:00:00Z',
    acceptanceDeadline: '2024-02-05T18:00:00Z',
    clientId: '2',
    clientName: '张三公司',
    deliveredBy: '王项目经理',
    notes: '首次交付版本，包含所有基础功能',
    attachments: [],
    revisionHistory: [],
    metadata: {},
    createdAt: '2024-01-20T10:00:00Z',
    updatedAt: '2024-01-24T16:30:00Z',
  },
  {
    id: 'package_2',
    projectId: 'proj_2',
    projectName: '电商平台',
    name: '电商平台 v2.1',
    description: '多商户电商平台，支持移动端和PC端',
    version: '2.1',
    status: DeliveryStatus.DELIVERED,
    deliverables: [],
    checklist: [],
    qualityReport: {} as any,
    acceptanceTests: [],
    deliveryDate: '2024-01-20T18:00:00Z',
    acceptanceDeadline: '2024-01-30T18:00:00Z',
    acceptedDate: '2024-01-28T14:20:00Z',
    clientId: '3',
    clientName: '李四企业',
    deliveredBy: '赵项目经理',
    acceptedBy: '李四',
    notes: '功能增强版本，新增移动端支持',
    attachments: [],
    revisionHistory: [],
    metadata: {},
    createdAt: '2024-01-15T09:00:00Z',
    updatedAt: '2024-01-28T14:20:00Z',
  },
]

const DeliveryManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview')
  const [packages, setPackages] = useState<DeliveryPackage[]>(mockDeliveryPackages)
  const [selectedPackage, setSelectedPackage] = useState<DeliveryPackage | null>(null)
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [form] = Form.useForm()

  const getStatusColor = (status: DeliveryStatus) => {
    const colors = {
      [DeliveryStatus.PREPARING]: 'blue',
      [DeliveryStatus.READY_FOR_DELIVERY]: 'orange',
      [DeliveryStatus.IN_DELIVERY]: 'processing',
      [DeliveryStatus.DELIVERED]: 'success',
      [DeliveryStatus.ACCEPTED]: 'green',
      [DeliveryStatus.REJECTED]: 'error',
      [DeliveryStatus.REVISION_REQUIRED]: 'warning',
    }
    return colors[status]
  }

  const getStatusText = (status: DeliveryStatus) => {
    const texts = {
      [DeliveryStatus.PREPARING]: '准备中',
      [DeliveryStatus.READY_FOR_DELIVERY]: '待交付',
      [DeliveryStatus.IN_DELIVERY]: '交付中',
      [DeliveryStatus.DELIVERED]: '已交付',
      [DeliveryStatus.ACCEPTED]: '已验收',
      [DeliveryStatus.REJECTED]: '已拒绝',
      [DeliveryStatus.REVISION_REQUIRED]: '需要修改',
    }
    return texts[status]
  }

  const getDaysRemaining = (deadline: string) => {
    const days = Math.ceil((new Date(deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    return days
  }

  const handlePackageCreate = async (values: any) => {
    try {
      const newPackage: DeliveryPackage = {
        id: `package_${Date.now()}`,
        projectId: values.projectId,
        projectName: values.projectName,
        name: values.name,
        description: values.description,
        version: values.version,
        status: DeliveryStatus.PREPARING,
        deliverables: [],
        checklist: [],
        qualityReport: {} as any,
        acceptanceTests: [],
        deliveryDate: values.deliveryDate.toISOString(),
        acceptanceDeadline: values.acceptanceDeadline.toISOString(),
        clientId: values.clientId,
        clientName: values.clientName,
        deliveredBy: '当前用户',
        notes: values.notes || '',
        attachments: [],
        revisionHistory: [],
        metadata: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      setPackages(prev => [newPackage, ...prev])
      setCreateModalVisible(false)
      form.resetFields()
      message.success('交付包创建成功')
    } catch (error) {
      message.error('创建失败')
    }
  }

  const handlePackageView = (pkg: DeliveryPackage) => {
    setSelectedPackage(pkg)
    setActiveTab('details')
  }

  const packageColumns: ColumnsType<DeliveryPackage> = [
    {
      title: '交付包',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.projectName} • v{record.version}
          </Text>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '客户',
      dataIndex: 'clientName',
      key: 'clientName',
    },
    {
      title: '交付日期',
      dataIndex: 'deliveryDate',
      key: 'deliveryDate',
      render: (date) => new Date(date).toLocaleDateString('zh-CN'),
    },
    {
      title: '验收截止',
      dataIndex: 'acceptanceDeadline',
      key: 'acceptanceDeadline',
      render: (date, record) => {
        const daysRemaining = getDaysRemaining(date)
        const isUrgent = daysRemaining <= 3 && record.status !== DeliveryStatus.ACCEPTED
        
        return (
          <div>
            <Text type={isUrgent ? 'danger' : 'secondary'}>
              {new Date(date).toLocaleDateString('zh-CN')}
            </Text>
            {isUrgent && (
              <div>
                <Tag color="red" size="small">
                  {daysRemaining > 0 ? `${daysRemaining}天后到期` : '已逾期'}
                </Tag>
              </div>
            )}
          </div>
        )
      },
    },
    {
      title: '负责人',
      dataIndex: 'deliveredBy',
      key: 'deliveredBy',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handlePackageView(record)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
          >
            编辑
          </Button>
        </Space>
      ),
    },
  ]

  // Calculate statistics
  const totalPackages = packages.length
  const deliveredPackages = packages.filter(p => p.status === DeliveryStatus.DELIVERED || p.status === DeliveryStatus.ACCEPTED).length
  const acceptedPackages = packages.filter(p => p.status === DeliveryStatus.ACCEPTED).length
  const pendingPackages = packages.filter(p => [DeliveryStatus.PREPARING, DeliveryStatus.READY_FOR_DELIVERY, DeliveryStatus.IN_DELIVERY].includes(p.status)).length
  const urgentPackages = packages.filter(p => {
    const daysRemaining = getDaysRemaining(p.acceptanceDeadline)
    return daysRemaining <= 3 && p.status !== DeliveryStatus.ACCEPTED
  }).length

  return (
    <div>
      {/* Header */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={2}>交付验收管理</Title>
        </Col>
        <Col>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建交付包
          </Button>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <Space>
                <DeliveredProcedureOutlined />
                概览
              </Space>
            }
            key="overview"
          >
            {/* Statistics */}
            <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
              <Col xs={24} sm={6}>
                <Card>
                  <Statistic
                    title="总交付包"
                    value={totalPackages}
                    prefix={<FileTextOutlined />}
                    suffix="个"
                  />
                </Card>
              </Col>
              <Col xs={24} sm={6}>
                <Card>
                  <Statistic
                    title="已交付"
                    value={deliveredPackages}
                    prefix={<CheckCircleOutlined />}
                    suffix="个"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={6}>
                <Card>
                  <Statistic
                    title="已验收"
                    value={acceptedPackages}
                    prefix={<QualityTimeOutlined />}
                    suffix="个"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={6}>
                <Card>
                  <Statistic
                    title="紧急处理"
                    value={urgentPackages}
                    prefix={<ExclamationCircleOutlined />}
                    suffix="个"
                    valueStyle={{ color: '#ff4d4f' }}
                  />
                </Card>
              </Col>
            </Row>

            {/* Progress */}
            <Card style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <Text strong>交付完成率</Text>
                <Text>{deliveredPackages}/{totalPackages}</Text>
              </div>
              <Progress
                percent={totalPackages > 0 ? Math.round((deliveredPackages / totalPackages) * 100) : 0}
                strokeColor="#52c41a"
              />
              
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8, marginTop: 16 }}>
                <Text strong>验收完成率</Text>
                <Text>{acceptedPackages}/{totalPackages}</Text>
              </div>
              <Progress
                percent={totalPackages > 0 ? Math.round((acceptedPackages / totalPackages) * 100) : 0}
                strokeColor="#1890ff"
              />
            </Card>

            {/* Alerts */}
            {urgentPackages > 0 && (
              <Alert
                message="紧急提醒"
                description={`有 ${urgentPackages} 个交付包即将到期或已逾期，请及时处理`}
                type="error"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}

            {/* Package List */}
            <Card title="交付包列表">
              <Table
                columns={packageColumns}
                dataSource={packages}
                rowKey="id"
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 个交付包`,
                }}
              />
            </Card>
          </TabPane>

          {selectedPackage && (
            <TabPane
              tab={
                <Space>
                  <EyeOutlined />
                  交付详情
                </Space>
              }
              key="details"
            >
              <Card title={selectedPackage.name} style={{ marginBottom: 16 }}>
                <Row gutter={[16, 16]}>
                  <Col xs={24} md={12}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text strong>项目:</Text> {selectedPackage.projectName}
                      </div>
                      <div>
                        <Text strong>版本:</Text> v{selectedPackage.version}
                      </div>
                      <div>
                        <Text strong>客户:</Text> {selectedPackage.clientName}
                      </div>
                      <div>
                        <Text strong>状态:</Text>{' '}
                        <Tag color={getStatusColor(selectedPackage.status)}>
                          {getStatusText(selectedPackage.status)}
                        </Tag>
                      </div>
                      <div>
                        <Text strong>描述:</Text>
                        <br />
                        <Text>{selectedPackage.description}</Text>
                      </div>
                    </Space>
                  </Col>
                  <Col xs={24} md={12}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text strong>交付日期:</Text>{' '}
                        {new Date(selectedPackage.deliveryDate).toLocaleString('zh-CN')}
                      </div>
                      <div>
                        <Text strong>验收截止:</Text>{' '}
                        {new Date(selectedPackage.acceptanceDeadline).toLocaleString('zh-CN')}
                      </div>
                      {selectedPackage.acceptedDate && (
                        <div>
                          <Text strong>验收时间:</Text>{' '}
                          <Text type="success">
                            {new Date(selectedPackage.acceptedDate).toLocaleString('zh-CN')}
                          </Text>
                        </div>
                      )}
                      <div>
                        <Text strong>负责人:</Text> {selectedPackage.deliveredBy}
                      </div>
                      {selectedPackage.acceptedBy && (
                        <div>
                          <Text strong>验收人:</Text> {selectedPackage.acceptedBy}
                        </div>
                      )}
                    </Space>
                  </Col>
                </Row>
              </Card>

              <Tabs defaultActiveKey="checklist">
                <TabPane
                  tab={
                    <Space>
                      <CheckCircleOutlined />
                      交付清单
                    </Space>
                  }
                  key="checklist"
                >
                  <DeliveryChecklist
                    packageId={selectedPackage.id}
                    items={selectedPackage.checklist}
                    onItemUpdate={(itemId, updates) => message.info('清单项更新功能开发中')}
                    onItemCreate={(item) => message.info('清单项创建功能开发中')}
                    onItemDelete={(itemId) => message.info('清单项删除功能开发中')}
                  />
                </TabPane>

                <TabPane
                  tab={
                    <Space>
                      <UploadOutlined />
                      文件管理
                    </Space>
                  }
                  key="files"
                >
                  <FileUploadPreview
                    deliverableId={selectedPackage.id}
                    files={[]}
                    onFileUpload={async (file, metadata) => message.info('文件上传功能开发中')}
                    onFileDelete={(fileId) => message.info('文件删除功能开发中')}
                    onFileUpdate={(fileId, updates) => message.info('文件更新功能开发中')}
                    onFileShare={(fileId, shareSettings) => message.info('文件分享功能开发中')}
                  />
                </TabPane>

                <TabPane
                  tab={
                    <Space>
                      <QualityTimeOutlined />
                      质量控制
                    </Space>
                  }
                  key="quality"
                >
                  <QualityControl
                    packageId={selectedPackage.id}
                    qualityReport={selectedPackage.qualityReport}
                    onReportUpdate={(updates) => message.info('质量报告更新功能开发中')}
                    onCriteriaUpdate={(criteriaId, updates) => message.info('评估标准更新功能开发中')}
                    onIssueCreate={(issue) => message.info('质量问题创建功能开发中')}
                    onIssueUpdate={(issueId, updates) => message.info('质量问题更新功能开发中')}
                  />
                </TabPane>

                <TabPane
                  tab={
                    <Space>
                      <ClockCircleOutlined />
                      验收流程
                    </Space>
                  }
                  key="workflow"
                >
                  <AcceptanceWorkflow
                    packageId={selectedPackage.id}
                    workflow={{} as any}
                    onStepAction={(stepId, action, data) => message.info('工作流操作功能开发中')}
                    onWorkflowUpdate={(updates) => message.info('工作流更新功能开发中')}
                    currentUserId="user_1"
                  />
                </TabPane>
              </Tabs>
            </TabPane>
          )}
        </Tabs>
      </Card>

      {/* Create Package Modal */}
      <Modal
        title="创建交付包"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form form={form} onFinish={handlePackageCreate} layout="vertical">
          <Form.Item
            name="name"
            label="交付包名称"
            rules={[{ required: true, message: '请输入交付包名称' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea rows={3} />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="projectName"
                label="项目名称"
                rules={[{ required: true, message: '请输入项目名称' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="version"
                label="版本号"
                rules={[{ required: true, message: '请输入版本号' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="clientName"
            label="客户名称"
            rules={[{ required: true, message: '请输入客户名称' }]}
          >
            <Input />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="deliveryDate"
                label="计划交付日期"
                rules={[{ required: true, message: '请选择交付日期' }]}
              >
                <DatePicker showTime style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="acceptanceDeadline"
                label="验收截止日期"
                rules={[{ required: true, message: '请选择验收截止日期' }]}
              >
                <DatePicker showTime style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="notes" label="备注">
            <TextArea rows={2} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default DeliveryManagement
