import React, { useState } from 'react'
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Table,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Drawer,
  Badge,
} from 'antd'
import {
  SmileOutlined,
  MessageOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  SettingOutlined,
  PlusOutlined,
  EyeOutlined,
  EditOutlined,
  TrophyOutlined,
} from '@ant-design/icons'
import { CustomerFeedback, FeedbackStatus, FeedbackType, NPSSurvey } from '../../types/feedback'
import SatisfactionDashboard from '../../components/feedback/SatisfactionDashboard'
import NPSRating from '../../components/feedback/NPSRating'
import FeedbackForm from '../../components/feedback/FeedbackForm'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography
const { TabPane } = Tabs
const { TextArea } = Input
const { Option } = Select

// Mock feedback data
const mockFeedbacks: CustomerFeedback[] = [
  {
    id: 'feedback_1',
    customerId: 'customer_1',
    customerName: '张三',
    customerEmail: '<EMAIL>',
    projectId: 'proj_1',
    projectName: '企业管理系统',
    type: FeedbackType.SATISFACTION,
    status: FeedbackStatus.RESOLVED,
    title: '整体服务很满意',
    content: '项目按时交付，功能符合预期，团队服务很专业',
    rating: 5,
    category: '服务质量',
    priority: 'medium',
    tags: ['满意', '按时交付'],
    attachments: [],
    responses: [],
    source: 'web',
    metadata: {},
    createdAt: '2024-01-22T10:30:00Z',
    updatedAt: '2024-01-22T15:20:00Z',
  },
  {
    id: 'feedback_2',
    customerId: 'customer_2',
    customerName: '李四',
    customerEmail: '<EMAIL>',
    type: FeedbackType.BUG_REPORT,
    status: FeedbackStatus.IN_PROGRESS,
    title: '登录页面响应慢',
    content: '用户登录时页面加载时间超过5秒，影响使用体验',
    priority: 'high',
    category: '产品功能',
    tags: ['性能', '登录'],
    attachments: [],
    responses: [],
    assignedTo: '技术支持团队',
    source: 'web',
    metadata: {},
    createdAt: '2024-01-22T09:15:00Z',
    updatedAt: '2024-01-22T14:30:00Z',
  },
]

const FeedbackManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard')
  const [feedbacks, setFeedbacks] = useState<CustomerFeedback[]>(mockFeedbacks)
  const [selectedFeedback, setSelectedFeedback] = useState<CustomerFeedback | null>(null)
  const [feedbackModalVisible, setFeedbackModalVisible] = useState(false)
  const [responseModalVisible, setResponseModalVisible] = useState(false)
  const [npsDrawerVisible, setNpsDrawerVisible] = useState(false)
  const [feedbackFormVisible, setFeedbackFormVisible] = useState(false)
  const [form] = Form.useForm()

  const getStatusColor = (status: FeedbackStatus) => {
    const colors = {
      [FeedbackStatus.PENDING]: 'orange',
      [FeedbackStatus.REVIEWED]: 'blue',
      [FeedbackStatus.IN_PROGRESS]: 'processing',
      [FeedbackStatus.RESOLVED]: 'success',
      [FeedbackStatus.CLOSED]: 'default',
      [FeedbackStatus.REJECTED]: 'error',
    }
    return colors[status]
  }

  const getStatusText = (status: FeedbackStatus) => {
    const texts = {
      [FeedbackStatus.PENDING]: '待处理',
      [FeedbackStatus.REVIEWED]: '已查看',
      [FeedbackStatus.IN_PROGRESS]: '处理中',
      [FeedbackStatus.RESOLVED]: '已解决',
      [FeedbackStatus.CLOSED]: '已关闭',
      [FeedbackStatus.REJECTED]: '已拒绝',
    }
    return texts[status]
  }

  const getTypeColor = (type: FeedbackType) => {
    const colors = {
      [FeedbackType.NPS]: 'purple',
      [FeedbackType.SATISFACTION]: 'green',
      [FeedbackType.FEATURE_REQUEST]: 'blue',
      [FeedbackType.BUG_REPORT]: 'red',
      [FeedbackType.GENERAL]: 'default',
      [FeedbackType.COMPLAINT]: 'orange',
      [FeedbackType.COMPLIMENT]: 'gold',
    }
    return colors[type]
  }

  const getTypeText = (type: FeedbackType) => {
    const texts = {
      [FeedbackType.NPS]: 'NPS评分',
      [FeedbackType.SATISFACTION]: '满意度',
      [FeedbackType.FEATURE_REQUEST]: '功能建议',
      [FeedbackType.BUG_REPORT]: '问题报告',
      [FeedbackType.GENERAL]: '一般反馈',
      [FeedbackType.COMPLAINT]: '投诉',
      [FeedbackType.COMPLIMENT]: '表扬',
    }
    return texts[type]
  }

  const getPriorityColor = (priority: string) => {
    const colors = {
      low: 'green',
      medium: 'orange',
      high: 'red',
      critical: 'purple',
    }
    return colors[priority as keyof typeof colors]
  }

  const handleFeedbackView = (feedback: CustomerFeedback) => {
    setSelectedFeedback(feedback)
    setFeedbackModalVisible(true)
  }

  const handleFeedbackResponse = (feedback: CustomerFeedback) => {
    setSelectedFeedback(feedback)
    setResponseModalVisible(true)
  }

  const handleResponseSubmit = async (values: any) => {
    try {
      // Update feedback status
      setFeedbacks(prev =>
        prev.map(f =>
          f.id === selectedFeedback?.id
            ? {
                ...f,
                status: values.status,
                responses: [
                  ...f.responses,
                  {
                    id: `response_${Date.now()}`,
                    feedbackId: f.id,
                    content: values.response,
                    authorId: 'admin_1',
                    authorName: '客服团队',
                    authorRole: '客服',
                    isInternal: false,
                    attachments: [],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                  },
                ],
                updatedAt: new Date().toISOString(),
              }
            : f
        )
      )
      setResponseModalVisible(false)
      form.resetFields()
      message.success('回复已发送')
    } catch (error) {
      message.error('回复失败')
    }
  }

  const handleFeedbackSubmit = async (feedback: Omit<CustomerFeedback, 'id'>) => {
    try {
      const newFeedback: CustomerFeedback = {
        ...feedback,
        id: `feedback_${Date.now()}`,
      }
      setFeedbacks(prev => [newFeedback, ...prev])
      setFeedbackFormVisible(false)
      message.success('反馈已提交')
    } catch (error) {
      message.error('提交失败')
    }
  }

  const feedbackColumns: ColumnsType<CustomerFeedback> = [
    {
      title: '客户',
      dataIndex: 'customerName',
      key: 'customerName',
      render: (name, record) => (
        <div>
          <Text strong>{name}</Text>
          {record.projectName && (
            <div>
              <Text type="secondary" style={{ fontSize: 12 }}>
                {record.projectName}
              </Text>
            </div>
          )}
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={getTypeColor(type)}>
          {getTypeText(type)}
        </Tag>
      ),
    },
    {
      title: '反馈内容',
      dataIndex: 'title',
      key: 'title',
      render: (title, record) => (
        <div>
          <Text strong>{title}</Text>
          {record.rating && (
            <div style={{ marginTop: 4 }}>
              <Space>
                {Array.from({ length: record.rating }, (_, i) => (
                  <SmileOutlined key={i} style={{ color: '#faad14' }} />
                ))}
                <Text style={{ fontSize: 12 }}>({record.rating}/5)</Text>
              </Space>
            </div>
          )}
        </div>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => (
        <Tag color={getPriorityColor(priority)}>
          {priority}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString('zh-CN'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleFeedbackView(record)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<MessageOutlined />}
            onClick={() => handleFeedbackResponse(record)}
            disabled={record.status === FeedbackStatus.CLOSED}
          >
            回复
          </Button>
        </Space>
      ),
    },
  ]

  // Calculate statistics
  const totalFeedbacks = feedbacks.length
  const pendingFeedbacks = feedbacks.filter(f => f.status === FeedbackStatus.PENDING).length
  const resolvedFeedbacks = feedbacks.filter(f => f.status === FeedbackStatus.RESOLVED).length
  const averageRating = feedbacks
    .filter(f => f.rating)
    .reduce((sum, f) => sum + (f.rating || 0), 0) / feedbacks.filter(f => f.rating).length || 0

  return (
    <div>
      {/* Header */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={2}>客户满意度与反馈</Title>
        </Col>
        <Col>
          <Space>
            <Button
              icon={<TrophyOutlined />}
              onClick={() => setNpsDrawerVisible(true)}
            >
              NPS调研
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setFeedbackFormVisible(true)}
            >
              添加反馈
            </Button>
          </Space>
        </Col>
      </Row>

      {/* Quick Stats */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                {totalFeedbacks}
              </div>
              <div style={{ color: '#666' }}>总反馈数</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#faad14' }}>
                {pendingFeedbacks}
              </div>
              <div style={{ color: '#666' }}>待处理</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                {resolvedFeedbacks}
              </div>
              <div style={{ color: '#666' }}>已解决</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#722ed1' }}>
                {averageRating.toFixed(1)}
              </div>
              <div style={{ color: '#666' }}>平均评分</div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <Space>
                <BarChartOutlined />
                满意度分析
              </Space>
            }
            key="dashboard"
          >
            <SatisfactionDashboard
              data={{} as any}
              onPeriodChange={(period) => message.info(`切换到${period}视图`)}
              onDateRangeChange={(dates) => message.info('日期范围已更新')}
              onFeedbackClick={(feedbackId) => {
                const feedback = feedbacks.find(f => f.id === feedbackId)
                if (feedback) handleFeedbackView(feedback)
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <MessageOutlined />
                反馈管理
                {pendingFeedbacks > 0 && (
                  <Badge count={pendingFeedbacks} size="small" />
                )}
              </Space>
            }
            key="feedbacks"
          >
            <Table
              columns={feedbackColumns}
              dataSource={feedbacks}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条反馈`,
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <SettingOutlined />
                调研设置
              </Space>
            }
            key="surveys"
          >
            <Card title="NPS调研设置">
              <Text type="secondary">
                配置自动化调研触发器和调研模板...
              </Text>
            </Card>
          </TabPane>
        </Tabs>
      </Card>

      {/* Feedback Detail Modal */}
      <Modal
        title="反馈详情"
        open={feedbackModalVisible}
        onCancel={() => setFeedbackModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedFeedback && (
          <div>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Space>
                  <Tag color={getTypeColor(selectedFeedback.type)}>
                    {getTypeText(selectedFeedback.type)}
                  </Tag>
                  <Tag color={getStatusColor(selectedFeedback.status)}>
                    {getStatusText(selectedFeedback.status)}
                  </Tag>
                  <Tag color={getPriorityColor(selectedFeedback.priority)}>
                    {selectedFeedback.priority}
                  </Tag>
                </Space>
              </div>

              <div>
                <Text strong>客户:</Text> {selectedFeedback.customerName}
                {selectedFeedback.projectName && (
                  <>
                    <br />
                    <Text strong>项目:</Text> {selectedFeedback.projectName}
                  </>
                )}
              </div>

              <div>
                <Text strong>标题:</Text> {selectedFeedback.title}
              </div>

              {selectedFeedback.rating && (
                <div>
                  <Text strong>评分:</Text>
                  <Space style={{ marginLeft: 8 }}>
                    {Array.from({ length: selectedFeedback.rating }, (_, i) => (
                      <SmileOutlined key={i} style={{ color: '#faad14' }} />
                    ))}
                    <Text>({selectedFeedback.rating}/5)</Text>
                  </Space>
                </div>
              )}

              <div>
                <Text strong>内容:</Text>
                <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                  {selectedFeedback.content}
                </div>
              </div>

              <div>
                <Text strong>创建时间:</Text> {new Date(selectedFeedback.createdAt).toLocaleString('zh-CN')}
              </div>

              {selectedFeedback.responses.length > 0 && (
                <div>
                  <Text strong>回复记录:</Text>
                  {selectedFeedback.responses.map(response => (
                    <div key={response.id} style={{ marginTop: 8, padding: 12, backgroundColor: '#e6f7ff', borderRadius: 4 }}>
                      <div style={{ marginBottom: 4 }}>
                        <Text strong>{response.authorName}</Text>
                        <Text type="secondary" style={{ marginLeft: 8, fontSize: 12 }}>
                          {new Date(response.createdAt).toLocaleString('zh-CN')}
                        </Text>
                      </div>
                      <Text>{response.content}</Text>
                    </div>
                  ))}
                </div>
              )}
            </Space>
          </div>
        )}
      </Modal>

      {/* Response Modal */}
      <Modal
        title="回复反馈"
        open={responseModalVisible}
        onCancel={() => setResponseModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} onFinish={handleResponseSubmit} layout="vertical">
          <Form.Item
            name="response"
            label="回复内容"
            rules={[{ required: true, message: '请输入回复内容' }]}
          >
            <TextArea rows={4} placeholder="请输入回复内容..." />
          </Form.Item>

          <Form.Item
            name="status"
            label="更新状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select>
              <Option value={FeedbackStatus.REVIEWED}>已查看</Option>
              <Option value={FeedbackStatus.IN_PROGRESS}>处理中</Option>
              <Option value={FeedbackStatus.RESOLVED}>已解决</Option>
              <Option value={FeedbackStatus.CLOSED}>已关闭</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* NPS Drawer */}
      <Drawer
        title="NPS调研"
        placement="right"
        width={600}
        open={npsDrawerVisible}
        onClose={() => setNpsDrawerVisible(false)}
      >
        <NPSRating
          onSubmit={async (response) => {
            message.success('NPS评分已提交')
            setNpsDrawerVisible(false)
          }}
          showAnalytics={true}
        />
      </Drawer>

      {/* Feedback Form Drawer */}
      <Drawer
        title="添加反馈"
        placement="right"
        width={800}
        open={feedbackFormVisible}
        onClose={() => setFeedbackFormVisible(false)}
      >
        <FeedbackForm
          onSubmit={handleFeedbackSubmit}
          onCancel={() => setFeedbackFormVisible(false)}
        />
      </Drawer>
    </div>
  )
}

export default FeedbackManagement
