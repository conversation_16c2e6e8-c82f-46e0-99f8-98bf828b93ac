import React, { useState } from 'react'
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Table,
  Tag,
  Progress,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  message,
  Drawer,
} from 'antd'
import {
  ProjectOutlined,
  PlusOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  CalendarOutlined,
  Bar<PERSON><PERSON>Outlined,
  FlagOutlined,
  TeamOutlined,
} from '@ant-design/icons'
import { Project, ProjectStatus, ProjectPriority, Task, Milestone } from '../../types/projects'
import ProjectDashboard from '../../components/projects/ProjectDashboard'
import GanttChart from '../../components/projects/GanttChart'
import MilestoneTracker from '../../components/projects/MilestoneTracker'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'

const { Title } = Typography
const { TabPane } = Tabs
const { TextArea } = Input
const { Option } = Select
const { RangePicker } = DatePicker

// Mock projects data
const mockProjects: Project[] = [
  {
    id: 'proj_1',
    name: '企业管理系统开发',
    description: '为客户开发完整的企业管理系统，包含人事、财务、项目管理等模块',
    status: ProjectStatus.IN_PROGRESS,
    priority: ProjectPriority.HIGH,
    progress: 65,
    startDate: '2024-01-15T00:00:00Z',
    endDate: '2024-04-15T00:00:00Z',
    actualStartDate: '2024-01-15T00:00:00Z',
    budget: 500000,
    actualCost: 320000,
    currency: 'CNY',
    clientId: '2',
    clientName: '张三公司',
    managerId: '5',
    managerName: '赵项目经理',
    teamMembers: [],
    tasks: [],
    milestones: [],
    deliverables: [],
    risks: [],
    documents: [],
    tags: ['ERP', '企业应用'],
    metadata: {},
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-02-01T00:00:00Z',
  },
  {
    id: 'proj_2',
    name: '电商平台开发',
    description: '多商户电商平台，支持移动端和PC端',
    status: ProjectStatus.IN_PROGRESS,
    priority: ProjectPriority.MEDIUM,
    progress: 40,
    startDate: '2024-01-20T00:00:00Z',
    endDate: '2024-05-20T00:00:00Z',
    actualStartDate: '2024-01-22T00:00:00Z',
    budget: 800000,
    actualCost: 280000,
    currency: 'CNY',
    clientId: '3',
    clientName: '李四企业',
    managerId: '5',
    managerName: '赵项目经理',
    teamMembers: [],
    tasks: [],
    milestones: [],
    deliverables: [],
    risks: [],
    documents: [],
    tags: ['电商', '移动端'],
    metadata: {},
    createdAt: '2024-01-20T00:00:00Z',
    updatedAt: '2024-02-01T00:00:00Z',
  },
]

const ProjectManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard')
  const [projects, setProjects] = useState<Project[]>(mockProjects)
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [ganttDrawerVisible, setGanttDrawerVisible] = useState(false)
  const [milestoneDrawerVisible, setMilestoneDrawerVisible] = useState(false)
  const [form] = Form.useForm()

  const getStatusColor = (status: ProjectStatus) => {
    const colors = {
      [ProjectStatus.PLANNING]: 'blue',
      [ProjectStatus.IN_PROGRESS]: 'processing',
      [ProjectStatus.ON_HOLD]: 'warning',
      [ProjectStatus.COMPLETED]: 'success',
      [ProjectStatus.CANCELLED]: 'error',
      [ProjectStatus.DELAYED]: 'error',
    }
    return colors[status]
  }

  const getStatusText = (status: ProjectStatus) => {
    const texts = {
      [ProjectStatus.PLANNING]: '规划中',
      [ProjectStatus.IN_PROGRESS]: '进行中',
      [ProjectStatus.ON_HOLD]: '暂停',
      [ProjectStatus.COMPLETED]: '已完成',
      [ProjectStatus.CANCELLED]: '已取消',
      [ProjectStatus.DELAYED]: '延期',
    }
    return texts[status]
  }

  const getPriorityColor = (priority: ProjectPriority) => {
    const colors = {
      [ProjectPriority.LOW]: 'green',
      [ProjectPriority.MEDIUM]: 'orange',
      [ProjectPriority.HIGH]: 'red',
      [ProjectPriority.CRITICAL]: 'purple',
    }
    return colors[priority]
  }

  const getPriorityText = (priority: ProjectPriority) => {
    const texts = {
      [ProjectPriority.LOW]: '低',
      [ProjectPriority.MEDIUM]: '中',
      [ProjectPriority.HIGH]: '高',
      [ProjectPriority.CRITICAL]: '紧急',
    }
    return texts[priority]
  }

  const formatCurrency = (amount: number) => {
    return `¥${amount.toLocaleString()}`
  }

  const handleProjectCreate = async (values: any) => {
    try {
      const newProject: Project = {
        id: `proj_${Date.now()}`,
        name: values.name,
        description: values.description,
        status: ProjectStatus.PLANNING,
        priority: values.priority,
        progress: 0,
        startDate: values.dateRange[0].toISOString(),
        endDate: values.dateRange[1].toISOString(),
        budget: values.budget,
        actualCost: 0,
        currency: 'CNY',
        clientId: values.clientId,
        clientName: values.clientName,
        managerId: values.managerId,
        managerName: values.managerName,
        teamMembers: [],
        tasks: [],
        milestones: [],
        deliverables: [],
        risks: [],
        documents: [],
        tags: values.tags || [],
        metadata: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      setProjects(prev => [newProject, ...prev])
      setCreateModalVisible(false)
      form.resetFields()
      message.success('项目创建成功')
    } catch (error) {
      message.error('创建失败')
    }
  }

  const handleProjectUpdate = async (values: any) => {
    if (!selectedProject) return

    try {
      const updatedProject = {
        ...selectedProject,
        name: values.name,
        description: values.description,
        priority: values.priority,
        startDate: values.dateRange[0].toISOString(),
        endDate: values.dateRange[1].toISOString(),
        budget: values.budget,
        clientName: values.clientName,
        managerName: values.managerName,
        tags: values.tags || [],
        updatedAt: new Date().toISOString(),
      }

      setProjects(prev =>
        prev.map(p => p.id === selectedProject.id ? updatedProject : p)
      )
      setEditModalVisible(false)
      form.resetFields()
      message.success('项目更新成功')
    } catch (error) {
      message.error('更新失败')
    }
  }

  const handleProjectDelete = (projectId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个项目吗？此操作不可恢复。',
      onOk: () => {
        setProjects(prev => prev.filter(p => p.id !== projectId))
        message.success('项目已删除')
      },
    })
  }

  const handleProjectEdit = (project: Project) => {
    setSelectedProject(project)
    form.setFieldsValue({
      name: project.name,
      description: project.description,
      priority: project.priority,
      dateRange: [dayjs(project.startDate), dayjs(project.endDate)],
      budget: project.budget,
      clientName: project.clientName,
      managerName: project.managerName,
      tags: project.tags,
    })
    setEditModalVisible(true)
  }

  const projectColumns: ColumnsType<Project> = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ fontSize: 12, color: '#666' }}>{record.clientName}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => (
        <Tag color={getPriorityColor(priority)}>
          {getPriorityText(priority)}
        </Tag>
      ),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress) => (
        <Progress percent={progress} size="small" />
      ),
    },
    {
      title: '预算',
      dataIndex: 'budget',
      key: 'budget',
      render: (budget, record) => (
        <div>
          <div>{formatCurrency(budget)}</div>
          <div style={{ fontSize: 12, color: '#666' }}>
            已用: {formatCurrency(record.actualCost)}
          </div>
        </div>
      ),
    },
    {
      title: '项目经理',
      dataIndex: 'managerName',
      key: 'managerName',
    },
    {
      title: '截止日期',
      dataIndex: 'endDate',
      key: 'endDate',
      render: (date) => new Date(date).toLocaleDateString('zh-CN'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedProject(record)
              setActiveTab('details')
            }}
          />
          <Button
            type="text"
            icon={<CalendarOutlined />}
            onClick={() => {
              setSelectedProject(record)
              setGanttDrawerVisible(true)
            }}
          />
          <Button
            type="text"
            icon={<FlagOutlined />}
            onClick={() => {
              setSelectedProject(record)
              setMilestoneDrawerVisible(true)
            }}
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleProjectEdit(record)}
          />
          <Button
            type="text"
            icon={<DeleteOutlined />}
            danger
            onClick={() => handleProjectDelete(record.id)}
          />
        </Space>
      ),
    },
  ]

  return (
    <div>
      {/* Header */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={2}>项目管理</Title>
        </Col>
        <Col>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建项目
          </Button>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <Space>
                <BarChartOutlined />
                仪表板
              </Space>
            }
            key="dashboard"
          >
            <ProjectDashboard
              data={{} as any}
              onProjectClick={(projectId) => {
                if (projectId === 'all') {
                  setActiveTab('projects')
                } else {
                  const project = projects.find(p => p.id === projectId)
                  if (project) {
                    setSelectedProject(project)
                    setActiveTab('details')
                  }
                }
              }}
              onTaskClick={(taskId) => message.info(`查看任务: ${taskId}`)}
              onMilestoneClick={(milestoneId) => message.info(`查看里程碑: ${milestoneId}`)}
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <ProjectOutlined />
                项目列表
              </Space>
            }
            key="projects"
          >
            <Table
              columns={projectColumns}
              dataSource={projects}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个项目`,
              }}
            />
          </TabPane>

          {selectedProject && (
            <TabPane
              tab={
                <Space>
                  <EyeOutlined />
                  项目详情
                </Space>
              }
              key="details"
            >
              <Card title={selectedProject.name} style={{ marginBottom: 16 }}>
                <Row gutter={[16, 16]}>
                  <Col xs={24} md={12}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text strong>项目描述:</Text>
                        <br />
                        <Text>{selectedProject.description}</Text>
                      </div>
                      <div>
                        <Text strong>客户:</Text> {selectedProject.clientName}
                      </div>
                      <div>
                        <Text strong>项目经理:</Text> {selectedProject.managerName}
                      </div>
                      <div>
                        <Text strong>状态:</Text>{' '}
                        <Tag color={getStatusColor(selectedProject.status)}>
                          {getStatusText(selectedProject.status)}
                        </Tag>
                      </div>
                      <div>
                        <Text strong>优先级:</Text>{' '}
                        <Tag color={getPriorityColor(selectedProject.priority)}>
                          {getPriorityText(selectedProject.priority)}
                        </Tag>
                      </div>
                    </Space>
                  </Col>
                  <Col xs={24} md={12}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text strong>项目进度:</Text>
                        <Progress percent={selectedProject.progress} />
                      </div>
                      <div>
                        <Text strong>预算:</Text> {formatCurrency(selectedProject.budget)}
                      </div>
                      <div>
                        <Text strong>已用成本:</Text> {formatCurrency(selectedProject.actualCost)}
                      </div>
                      <div>
                        <Text strong>开始日期:</Text>{' '}
                        {new Date(selectedProject.startDate).toLocaleDateString('zh-CN')}
                      </div>
                      <div>
                        <Text strong>结束日期:</Text>{' '}
                        {new Date(selectedProject.endDate).toLocaleDateString('zh-CN')}
                      </div>
                    </Space>
                  </Col>
                </Row>
              </Card>

              <Row gutter={[16, 16]}>
                <Col xs={24} lg={12}>
                  <Card
                    title="甘特图"
                    extra={
                      <Button
                        onClick={() => setGanttDrawerVisible(true)}
                        icon={<CalendarOutlined />}
                      >
                        查看详细
                      </Button>
                    }
                  >
                    <div style={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <Text type="secondary">点击"查看详细"打开甘特图</Text>
                    </div>
                  </Card>
                </Col>
                <Col xs={24} lg={12}>
                  <Card
                    title="里程碑跟踪"
                    extra={
                      <Button
                        onClick={() => setMilestoneDrawerVisible(true)}
                        icon={<FlagOutlined />}
                      >
                        查看详细
                      </Button>
                    }
                  >
                    <div style={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <Text type="secondary">点击"查看详细"打开里程碑跟踪</Text>
                    </div>
                  </Card>
                </Col>
              </Row>
            </TabPane>
          )}
        </Tabs>
      </Card>

      {/* Create Project Modal */}
      <Modal
        title="创建项目"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form form={form} onFinish={handleProjectCreate} layout="vertical">
          <Form.Item
            name="name"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item name="description" label="项目描述">
            <TextArea rows={3} />
          </Form.Item>

          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请选择优先级' }]}
          >
            <Select>
              <Option value={ProjectPriority.LOW}>低</Option>
              <Option value={ProjectPriority.MEDIUM}>中</Option>
              <Option value={ProjectPriority.HIGH}>高</Option>
              <Option value={ProjectPriority.CRITICAL}>紧急</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="dateRange"
            label="项目周期"
            rules={[{ required: true, message: '请选择项目周期' }]}
          >
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="budget"
            label="项目预算"
            rules={[{ required: true, message: '请输入项目预算' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value!.replace(/¥\s?|(,*)/g, '')}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="clientName"
                label="客户名称"
                rules={[{ required: true, message: '请输入客户名称' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="managerName"
                label="项目经理"
                rules={[{ required: true, message: '请输入项目经理' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="tags" label="项目标签">
            <Select mode="tags" placeholder="添加项目标签" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Edit Project Modal */}
      <Modal
        title="编辑项目"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form form={form} onFinish={handleProjectUpdate} layout="vertical">
          <Form.Item
            name="name"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item name="description" label="项目描述">
            <TextArea rows={3} />
          </Form.Item>

          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请选择优先级' }]}
          >
            <Select>
              <Option value={ProjectPriority.LOW}>低</Option>
              <Option value={ProjectPriority.MEDIUM}>中</Option>
              <Option value={ProjectPriority.HIGH}>高</Option>
              <Option value={ProjectPriority.CRITICAL}>紧急</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="dateRange"
            label="项目周期"
            rules={[{ required: true, message: '请选择项目周期' }]}
          >
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="budget"
            label="项目预算"
            rules={[{ required: true, message: '请输入项目预算' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value!.replace(/¥\s?|(,*)/g, '')}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="clientName"
                label="客户名称"
                rules={[{ required: true, message: '请输入客户名称' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="managerName"
                label="项目经理"
                rules={[{ required: true, message: '请输入项目经理' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="tags" label="项目标签">
            <Select mode="tags" placeholder="添加项目标签" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Gantt Chart Drawer */}
      <Drawer
        title="甘特图"
        placement="right"
        width="90%"
        open={ganttDrawerVisible}
        onClose={() => setGanttDrawerVisible(false)}
      >
        {selectedProject && (
          <GanttChart
            tasks={selectedProject.tasks}
            milestones={selectedProject.milestones}
            onTaskUpdate={(taskId, updates) => message.info('任务更新功能开发中')}
            onTaskCreate={(task) => message.info('任务创建功能开发中')}
          />
        )}
      </Drawer>

      {/* Milestone Tracker Drawer */}
      <Drawer
        title="里程碑跟踪"
        placement="right"
        width="80%"
        open={milestoneDrawerVisible}
        onClose={() => setMilestoneDrawerVisible(false)}
      >
        {selectedProject && (
          <MilestoneTracker
            projectId={selectedProject.id}
            milestones={selectedProject.milestones}
            onMilestoneUpdate={(milestoneId, updates) => message.info('里程碑更新功能开发中')}
            onMilestoneCreate={(milestone) => message.info('里程碑创建功能开发中')}
            onMilestoneComplete={(milestoneId) => message.info('里程碑完成功能开发中')}
          />
        )}
      </Drawer>
    </div>
  )
}

export default ProjectManagement
