import React, { useState } from 'react'
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Tag,
  Modal,
  Form,
  Input,
  Rate,
  message,
  Steps,
  Alert,
  Progress,
} from 'antd'
import {
  EyeOutlined,
  CommentOutlined,
  HistoryOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  EditOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  UserOutlined,
} from '@ant-design/icons'
import { CustomSolution } from '../../types/solutions'
import { SolutionReview, ReviewStatus, ReviewAnnotation } from '../../types/customerReview'
import SolutionPreview from '../../components/customer/SolutionPreview'
import AnnotationTool from '../../components/customer/AnnotationTool'
import VersionComparison from '../../components/customer/VersionComparison'
import { useAuthStore } from '../../stores/authStore'

const { Title, Text } = Typography
const { TabPane } = Tabs
const { TextArea } = Input
const { Step } = Steps

// Mock solution data
const mockSolution: CustomSolution = {
  id: 'solution_1',
  name: '企业管理系统定制方案',
  description: '基于企业管理系统模板的定制化解决方案，包含用户管理、权限控制、数据分析等核心功能',
  requirementId: 'req_1',
  templateId: 'template_1',
  components: [],
  customComponents: [],
  configuration: {},
  estimatedCost: 180000,
  estimatedDuration: 95,
  complexity: 'high',
  status: 'approved' as any,
  version: '1.1',
  changelog: [],
  approvals: [],
  comments: [],
  attachments: [],
  tags: ['ERP', '定制开发'],
  metadata: {},
  createdBy: '1',
  assignedTo: '4',
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T14:30:00Z',
}

// Mock review data
const mockReview: SolutionReview = {
  id: 'review_1',
  solutionId: 'solution_1',
  solutionName: '企业管理系统定制方案',
  solutionVersion: '1.1',
  customerId: '2',
  customerName: '张三',
  status: ReviewStatus.IN_REVIEW,
  submittedAt: '2024-01-15T10:00:00Z',
  reviewDeadline: '2024-01-20T18:00:00Z',
  currentStep: 2,
  totalSteps: 4,
  annotations: [],
  approvals: [],
  revisions: [],
  attachments: [],
  metadata: {},
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T14:30:00Z',
}

const SolutionReviewPage: React.FC = () => {
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('preview')
  const [solution] = useState<CustomSolution>(mockSolution)
  const [review, setReview] = useState<SolutionReview>(mockReview)
  const [annotations, setAnnotations] = useState<ReviewAnnotation[]>([])
  const [approvalModalVisible, setApprovalModalVisible] = useState(false)
  const [rejectionModalVisible, setRejectionModalVisible] = useState(false)
  const [revisionModalVisible, setRevisionModalVisible] = useState(false)
  const [form] = Form.useForm()

  const handleApprove = async (values?: any) => {
    try {
      setReview(prev => ({
        ...prev,
        status: ReviewStatus.APPROVED,
        completedAt: new Date().toISOString(),
        currentStep: prev.totalSteps,
      }))
      setApprovalModalVisible(false)
      message.success('解决方案已批准')
    } catch (error) {
      message.error('批准失败')
    }
  }

  const handleReject = async (reason: string) => {
    try {
      setReview(prev => ({
        ...prev,
        status: ReviewStatus.REJECTED,
        completedAt: new Date().toISOString(),
      }))
      setRejectionModalVisible(false)
      message.success('解决方案已拒绝')
    } catch (error) {
      message.error('拒绝失败')
    }
  }

  const handleRequestRevision = async (changes: any[]) => {
    try {
      setReview(prev => ({
        ...prev,
        status: ReviewStatus.REVISION_REQUESTED,
        updatedAt: new Date().toISOString(),
      }))
      setRevisionModalVisible(false)
      message.success('修改请求已提交')
    } catch (error) {
      message.error('提交失败')
    }
  }

  const handleAddAnnotation = (annotation: any) => {
    const newAnnotation: ReviewAnnotation = {
      ...annotation,
      id: `annotation_${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
    setAnnotations(prev => [...prev, newAnnotation])
    message.success('注释已添加')
  }

  const handleUpdateAnnotation = (id: string, updates: Partial<ReviewAnnotation>) => {
    setAnnotations(prev =>
      prev.map(annotation =>
        annotation.id === id
          ? { ...annotation, ...updates, updatedAt: new Date().toISOString() }
          : annotation
      )
    )
  }

  const handleDeleteAnnotation = (id: string) => {
    setAnnotations(prev => prev.filter(annotation => annotation.id !== id))
    message.success('注释已删除')
  }

  const handleResolveAnnotation = (id: string) => {
    handleUpdateAnnotation(id, {
      status: 'resolved',
      resolvedAt: new Date().toISOString(),
      resolvedBy: user?.id,
    })
    message.success('注释已解决')
  }

  const getStatusColor = (status: ReviewStatus) => {
    const colors = {
      [ReviewStatus.PENDING]: 'default',
      [ReviewStatus.IN_REVIEW]: 'processing',
      [ReviewStatus.APPROVED]: 'success',
      [ReviewStatus.REJECTED]: 'error',
      [ReviewStatus.REVISION_REQUESTED]: 'warning',
      [ReviewStatus.EXPIRED]: 'error',
    }
    return colors[status]
  }

  const getStatusText = (status: ReviewStatus) => {
    const texts = {
      [ReviewStatus.PENDING]: '待审核',
      [ReviewStatus.IN_REVIEW]: '审核中',
      [ReviewStatus.APPROVED]: '已批准',
      [ReviewStatus.REJECTED]: '已拒绝',
      [ReviewStatus.REVISION_REQUESTED]: '需要修改',
      [ReviewStatus.EXPIRED]: '已过期',
    }
    return texts[status]
  }

  const calculateProgress = () => {
    return Math.round((review.currentStep / review.totalSteps) * 100)
  }

  const getDaysRemaining = () => {
    if (!review.reviewDeadline) return null
    const deadline = new Date(review.reviewDeadline)
    const now = new Date()
    const diffTime = deadline.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const daysRemaining = getDaysRemaining()
  const isExpiringSoon = daysRemaining !== null && daysRemaining <= 2
  const canTakeAction = review.status === ReviewStatus.IN_REVIEW

  const reviewSteps = [
    { title: '方案提交', description: '解决方案已提交审核' },
    { title: '客户审核', description: '客户正在审核方案' },
    { title: '反馈收集', description: '收集客户反馈和建议' },
    { title: '最终确认', description: '最终确认和批准' },
  ]

  return (
    <div>
      {/* Header */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space direction="vertical" size="small">
              <Title level={3} style={{ margin: 0 }}>
                解决方案审核
              </Title>
              <Space>
                <Tag color={getStatusColor(review.status)}>
                  {getStatusText(review.status)}
                </Tag>
                <Text type="secondary">审核ID: {review.id}</Text>
              </Space>
            </Space>
          </Col>
          <Col>
            <Space>
              {canTakeAction && (
                <>
                  <Button
                    type="primary"
                    icon={<CheckCircleOutlined />}
                    onClick={() => setApprovalModalVisible(true)}
                  >
                    批准
                  </Button>
                  <Button
                    danger
                    icon={<CloseCircleOutlined />}
                    onClick={() => setRejectionModalVisible(true)}
                  >
                    拒绝
                  </Button>
                  <Button
                    icon={<EditOutlined />}
                    onClick={() => setRevisionModalVisible(true)}
                  >
                    请求修改
                  </Button>
                </>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Progress and Alerts */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} lg={18}>
          <Card title="审核进度" size="small">
            <Steps current={review.currentStep - 1} items={reviewSteps} />
            <div style={{ marginTop: 16 }}>
              <Progress percent={calculateProgress()} />
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={6}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Card>
              <Statistic
                title="剩余时间"
                value={daysRemaining || 0}
                suffix="天"
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: isExpiringSoon ? '#ff4d4f' : '#52c41a' }}
              />
            </Card>
            {isExpiringSoon && (
              <Alert
                message="截止日期临近"
                description="请尽快完成审核"
                type="warning"
                showIcon
              />
            )}
          </Space>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <Space>
                <EyeOutlined />
                方案预览
              </Space>
            }
            key="preview"
          >
            <SolutionPreview
              solution={solution}
              review={review}
              onApprove={() => setApprovalModalVisible(true)}
              onReject={(reason) => handleReject(reason)}
              onRequestRevision={handleRequestRevision}
              onAddAnnotation={handleAddAnnotation}
              readonly={!canTakeAction}
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <CommentOutlined />
                注释反馈
                {annotations.length > 0 && (
                  <Tag size="small">{annotations.length}</Tag>
                )}
              </Space>
            }
            key="annotations"
          >
            <AnnotationTool
              annotations={annotations}
              onAddAnnotation={handleAddAnnotation}
              onUpdateAnnotation={handleUpdateAnnotation}
              onDeleteAnnotation={handleDeleteAnnotation}
              onResolveAnnotation={handleResolveAnnotation}
              currentUserId={user?.id || '2'}
              currentUserName={user?.name || '张三'}
              readonly={!canTakeAction}
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <HistoryOutlined />
                版本对比
              </Space>
            }
            key="versions"
          >
            <VersionComparison
              versions={['1.0', '1.1']}
              onCompare={(base, compare) => message.info(`对比 ${base} 和 ${compare}`)}
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <FileTextOutlined />
                审核记录
              </Space>
            }
            key="history"
          >
            <Card title="审核历史" size="small">
              <Steps
                direction="vertical"
                current={-1}
                items={[
                  {
                    title: '方案提交',
                    description: '解决方案已提交，等待客户审核',
                    status: 'finish',
                    icon: <UserOutlined />,
                  },
                  {
                    title: '开始审核',
                    description: '客户开始审核解决方案',
                    status: 'process',
                    icon: <EyeOutlined />,
                  },
                ]}
              />
            </Card>
          </TabPane>
        </Tabs>
      </Card>

      {/* Approval Modal */}
      <Modal
        title="批准解决方案"
        open={approvalModalVisible}
        onCancel={() => setApprovalModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} onFinish={handleApprove} layout="vertical">
          <Form.Item label="满意度评分" name="rating">
            <Rate />
          </Form.Item>
          <Form.Item label="批准意见" name="comments">
            <TextArea
              rows={4}
              placeholder="请输入您的批准意见（可选）"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Rejection Modal */}
      <Modal
        title="拒绝解决方案"
        open={rejectionModalVisible}
        onCancel={() => setRejectionModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} onFinish={(values) => handleReject(values.reason)} layout="vertical">
          <Form.Item
            label="拒绝原因"
            name="reason"
            rules={[{ required: true, message: '请输入拒绝原因' }]}
          >
            <TextArea
              rows={4}
              placeholder="请详细说明拒绝的原因"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Revision Request Modal */}
      <Modal
        title="请求修改"
        open={revisionModalVisible}
        onCancel={() => setRevisionModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form form={form} onFinish={(values) => handleRequestRevision(values.changes)} layout="vertical">
          <Form.Item
            label="修改要求"
            name="changes"
            rules={[{ required: true, message: '请输入修改要求' }]}
          >
            <TextArea
              rows={6}
              placeholder="请详细描述需要修改的内容和要求"
            />
          </Form.Item>
          <Form.Item label="优先级" name="priority">
            <Rate count={3} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default SolutionReviewPage
