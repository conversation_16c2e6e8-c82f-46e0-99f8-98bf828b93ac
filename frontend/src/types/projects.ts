export enum ProjectStatus {
  PLANNING = 'planning',
  IN_PROGRESS = 'in_progress',
  ON_HOLD = 'on_hold',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  DELAYED = 'delayed',
}

export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  REVIEW = 'review',
  DONE = 'done',
  BLOCKED = 'blocked',
}

export enum MilestoneStatus {
  UPCOMING = 'upcoming',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  OVERDUE = 'overdue',
}

export enum ProjectPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface Project {
  id: string
  name: string
  description: string
  status: ProjectStatus
  priority: ProjectPriority
  progress: number
  startDate: string
  endDate: string
  actualStartDate?: string
  actualEndDate?: string
  budget: number
  actualCost: number
  currency: string
  clientId: string
  clientName: string
  managerId: string
  managerName: string
  teamMembers: ProjectMember[]
  tasks: Task[]
  milestones: Milestone[]
  deliverables: Deliverable[]
  risks: Risk[]
  documents: ProjectDocument[]
  tags: string[]
  metadata: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface ProjectMember {
  id: string
  userId: string
  userName: string
  userAvatar?: string
  role: string
  department: string
  allocation: number // percentage
  hourlyRate?: number
  joinedAt: string
  leftAt?: string
  isActive: boolean
}

export interface Task {
  id: string
  projectId: string
  name: string
  description: string
  status: TaskStatus
  priority: ProjectPriority
  progress: number
  estimatedHours: number
  actualHours: number
  startDate: string
  endDate: string
  assigneeId?: string
  assigneeName?: string
  dependencies: string[]
  subtasks: Task[]
  comments: TaskComment[]
  attachments: string[]
  tags: string[]
  createdBy: string
  createdAt: string
  updatedAt: string
}

export interface TaskComment {
  id: string
  content: string
  authorId: string
  authorName: string
  authorAvatar?: string
  createdAt: string
  updatedAt: string
}

export interface Milestone {
  id: string
  projectId: string
  name: string
  description: string
  status: MilestoneStatus
  dueDate: string
  completedDate?: string
  deliverables: string[]
  criteria: string[]
  dependencies: string[]
  progress: number
  isKeyMilestone: boolean
  createdAt: string
  updatedAt: string
}

export interface Deliverable {
  id: string
  projectId: string
  name: string
  description: string
  type: 'document' | 'software' | 'service' | 'other'
  status: 'pending' | 'in_progress' | 'review' | 'approved' | 'delivered'
  dueDate: string
  deliveredDate?: string
  assigneeId: string
  assigneeName: string
  files: DeliverableFile[]
  reviewers: string[]
  approvals: DeliverableApproval[]
  createdAt: string
  updatedAt: string
}

export interface DeliverableFile {
  id: string
  name: string
  url: string
  size: number
  type: string
  version: string
  uploadedBy: string
  uploadedAt: string
}

export interface DeliverableApproval {
  id: string
  reviewerId: string
  reviewerName: string
  status: 'pending' | 'approved' | 'rejected'
  comments?: string
  reviewedAt?: string
}

export interface Risk {
  id: string
  projectId: string
  title: string
  description: string
  category: 'technical' | 'business' | 'resource' | 'schedule' | 'quality'
  probability: 'low' | 'medium' | 'high'
  impact: 'low' | 'medium' | 'high'
  severity: number // calculated from probability and impact
  status: 'identified' | 'analyzing' | 'mitigating' | 'monitoring' | 'closed'
  mitigation: string
  contingency: string
  ownerId: string
  ownerName: string
  identifiedDate: string
  targetDate?: string
  closedDate?: string
  createdAt: string
  updatedAt: string
}

export interface ProjectDocument {
  id: string
  projectId: string
  name: string
  description: string
  type: 'requirement' | 'design' | 'technical' | 'contract' | 'report' | 'other'
  url: string
  size: number
  version: string
  status: 'draft' | 'review' | 'approved' | 'archived'
  uploadedBy: string
  uploadedAt: string
  tags: string[]
}

export interface ProjectMetrics {
  totalProjects: number
  activeProjects: number
  completedProjects: number
  delayedProjects: number
  onTimeDeliveryRate: number
  budgetUtilization: number
  teamUtilization: number
  averageProjectDuration: number
  customerSatisfaction: number
}

export interface ProjectDashboard {
  overview: ProjectMetrics
  recentProjects: Project[]
  upcomingMilestones: Milestone[]
  overdueTasks: Task[]
  teamWorkload: TeamWorkload[]
  budgetSummary: BudgetSummary
  riskSummary: RiskSummary
}

export interface TeamWorkload {
  userId: string
  userName: string
  userAvatar?: string
  department: string
  totalHours: number
  allocatedHours: number
  utilization: number
  activeProjects: number
  overdueTasks: number
}

export interface BudgetSummary {
  totalBudget: number
  spentBudget: number
  remainingBudget: number
  projectedCost: number
  budgetUtilization: number
  costVariance: number
}

export interface RiskSummary {
  totalRisks: number
  highRisks: number
  mediumRisks: number
  lowRisks: number
  mitigatedRisks: number
  openRisks: number
}

// Gantt Chart specific types
export interface GanttTask {
  id: string
  name: string
  start: Date
  end: Date
  progress: number
  dependencies: string[]
  type: 'task' | 'milestone' | 'project'
  styles?: {
    backgroundColor?: string
    progressColor?: string
    progressSelectedColor?: string
  }
}

export interface GanttViewType {
  viewMode: 'day' | 'week' | 'month' | 'year'
  columnWidth: number
}

// Timeline and Progress types
export interface TimelineEvent {
  id: string
  projectId: string
  type: 'task_start' | 'task_complete' | 'milestone' | 'risk' | 'change' | 'meeting'
  title: string
  description: string
  date: string
  userId?: string
  userName?: string
  metadata?: Record<string, any>
}

export interface ProgressReport {
  id: string
  projectId: string
  reportDate: string
  overallProgress: number
  tasksCompleted: number
  totalTasks: number
  milestonesAchieved: number
  totalMilestones: number
  budgetSpent: number
  totalBudget: number
  issues: ProgressIssue[]
  achievements: string[]
  nextSteps: string[]
  reportedBy: string
  createdAt: string
}

export interface ProgressIssue {
  id: string
  title: string
  description: string
  severity: 'low' | 'medium' | 'high'
  status: 'open' | 'in_progress' | 'resolved'
  assigneeId?: string
  dueDate?: string
}

// Resource Management
export interface Resource {
  id: string
  name: string
  type: 'human' | 'equipment' | 'software' | 'facility'
  availability: ResourceAvailability[]
  cost: number
  costType: 'hourly' | 'daily' | 'fixed'
  skills?: string[]
  department?: string
  location?: string
}

export interface ResourceAvailability {
  startDate: string
  endDate: string
  availability: number // percentage
  projectId?: string
  reason?: string
}

export interface ResourceAllocation {
  id: string
  projectId: string
  resourceId: string
  resourceName: string
  startDate: string
  endDate: string
  allocation: number
  cost: number
  approvedBy: string
  approvedAt: string
}

// Project Templates
export interface ProjectTemplate {
  id: string
  name: string
  description: string
  category: string
  estimatedDuration: number
  estimatedCost: number
  tasks: TaskTemplate[]
  milestones: MilestoneTemplate[]
  deliverables: DeliverableTemplate[]
  requiredRoles: string[]
  isPublic: boolean
  usageCount: number
  rating: number
  createdBy: string
  createdAt: string
  updatedAt: string
}

export interface TaskTemplate {
  name: string
  description: string
  estimatedHours: number
  dependencies: string[]
  requiredRole: string
  deliverables: string[]
}

export interface MilestoneTemplate {
  name: string
  description: string
  daysFromStart: number
  deliverables: string[]
  criteria: string[]
}

export interface DeliverableTemplate {
  name: string
  description: string
  type: string
  daysFromStart: number
  requiredRole: string
}

// Change Management
export interface ChangeRequest {
  id: string
  projectId: string
  title: string
  description: string
  reason: string
  impact: ChangeImpact
  status: 'submitted' | 'reviewing' | 'approved' | 'rejected' | 'implemented'
  priority: ProjectPriority
  requestedBy: string
  requestedAt: string
  reviewedBy?: string
  reviewedAt?: string
  implementedAt?: string
  comments: ChangeComment[]
}

export interface ChangeImpact {
  scope: 'minor' | 'moderate' | 'major'
  schedule: number // days
  budget: number
  resources: string[]
  risks: string[]
}

export interface ChangeComment {
  id: string
  content: string
  authorId: string
  authorName: string
  createdAt: string
}
