export enum FeedbackType {
  NPS = 'nps',
  SATISFACTION = 'satisfaction',
  FEATURE_REQUEST = 'feature_request',
  BUG_REPORT = 'bug_report',
  GENERAL = 'general',
  COMPLAINT = 'complaint',
  COMPLIMENT = 'compliment',
}

export enum FeedbackStatus {
  PENDING = 'pending',
  REVIEWED = 'reviewed',
  IN_PROGRESS = 'in_progress',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
  REJECTED = 'rejected',
}

export enum SurveyStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ARCHIVED = 'archived',
}

export enum TriggerType {
  PROJECT_COMPLETION = 'project_completion',
  DELIVERY_ACCEPTANCE = 'delivery_acceptance',
  MILESTONE_REACHED = 'milestone_reached',
  SUPPORT_TICKET_RESOLVED = 'support_ticket_resolved',
  SCHEDULED = 'scheduled',
  MANUAL = 'manual',
}

export interface CustomerFeedback {
  id: string
  customerId: string
  customerName: string
  customerEmail: string
  projectId?: string
  projectName?: string
  type: FeedbackType
  status: FeedbackStatus
  title: string
  content: string
  rating?: number
  npsScore?: number
  category: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  tags: string[]
  attachments: FeedbackAttachment[]
  responses: FeedbackResponse[]
  assignedTo?: string
  assignedBy?: string
  assignedAt?: string
  resolvedAt?: string
  resolvedBy?: string
  resolution?: string
  source: 'web' | 'email' | 'phone' | 'survey' | 'chat'
  metadata: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface FeedbackAttachment {
  id: string
  feedbackId: string
  name: string
  url: string
  size: number
  type: string
  uploadedAt: string
}

export interface FeedbackResponse {
  id: string
  feedbackId: string
  content: string
  authorId: string
  authorName: string
  authorRole: string
  isInternal: boolean
  attachments: string[]
  createdAt: string
  updatedAt: string
}

export interface NPSSurvey {
  id: string
  name: string
  description: string
  status: SurveyStatus
  questions: SurveyQuestion[]
  triggers: SurveyTrigger[]
  targetAudience: SurveyAudience
  settings: SurveySettings
  responses: NPSResponse[]
  analytics: NPSAnalytics
  createdBy: string
  createdAt: string
  updatedAt: string
  startDate?: string
  endDate?: string
}

export interface SurveyQuestion {
  id: string
  surveyId: string
  type: 'nps' | 'rating' | 'text' | 'choice' | 'multiple_choice'
  title: string
  description?: string
  required: boolean
  order: number
  options?: SurveyOption[]
  validation?: QuestionValidation
}

export interface SurveyOption {
  id: string
  questionId: string
  text: string
  value: string | number
  order: number
}

export interface QuestionValidation {
  minLength?: number
  maxLength?: number
  pattern?: string
  min?: number
  max?: number
}

export interface SurveyTrigger {
  id: string
  surveyId: string
  type: TriggerType
  name: string
  description: string
  conditions: TriggerCondition[]
  delay: number // hours
  isActive: boolean
  createdAt: string
}

export interface TriggerCondition {
  id: string
  triggerId: string
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than'
  value: string | number
}

export interface SurveyAudience {
  customerSegments: string[]
  projectTypes: string[]
  excludeRecentResponders: boolean
  excludeDays: number
  maxResponsesPerCustomer: number
}

export interface SurveySettings {
  allowAnonymous: boolean
  requireLogin: boolean
  showProgress: boolean
  allowSkip: boolean
  randomizeQuestions: boolean
  customTheme?: SurveyTheme
  thankYouMessage: string
  redirectUrl?: string
}

export interface SurveyTheme {
  primaryColor: string
  backgroundColor: string
  textColor: string
  fontFamily: string
  logo?: string
}

export interface NPSResponse {
  id: string
  surveyId: string
  customerId: string
  customerName: string
  customerEmail: string
  projectId?: string
  projectName?: string
  npsScore: number
  answers: SurveyAnswer[]
  completedAt: string
  source: string
  ipAddress?: string
  userAgent?: string
  metadata: Record<string, any>
}

export interface SurveyAnswer {
  id: string
  responseId: string
  questionId: string
  questionType: string
  value: string | number | string[]
  text?: string
}

export interface NPSAnalytics {
  id: string
  surveyId: string
  period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  startDate: string
  endDate: string
  totalResponses: number
  npsScore: number
  promoters: number
  passives: number
  detractors: number
  promoterRate: number
  detractorRate: number
  responseRate: number
  trends: NPSTrend[]
  segments: NPSSegment[]
  insights: NPSInsight[]
  updatedAt: string
}

export interface NPSTrend {
  date: string
  npsScore: number
  responses: number
  promoters: number
  passives: number
  detractors: number
}

export interface NPSSegment {
  name: string
  criteria: string
  npsScore: number
  responses: number
  promoterRate: number
  detractorRate: number
}

export interface NPSInsight {
  id: string
  type: 'improvement' | 'concern' | 'opportunity' | 'trend'
  title: string
  description: string
  impact: 'low' | 'medium' | 'high'
  confidence: number
  recommendations: string[]
  data: Record<string, any>
}

export interface SatisfactionMetrics {
  overallSatisfaction: number
  npsScore: number
  responseRate: number
  totalFeedbacks: number
  resolvedFeedbacks: number
  averageResolutionTime: number
  customerRetentionRate: number
  repeatCustomerRate: number
  categoryBreakdown: CategoryMetrics[]
  trendData: SatisfactionTrend[]
  topIssues: IssueMetrics[]
  topCompliments: ComplimentMetrics[]
}

export interface CategoryMetrics {
  category: string
  count: number
  averageRating: number
  satisfactionRate: number
  resolutionRate: number
}

export interface SatisfactionTrend {
  date: string
  satisfaction: number
  nps: number
  feedbacks: number
  resolved: number
}

export interface IssueMetrics {
  issue: string
  count: number
  severity: string
  averageResolutionTime: number
  resolutionRate: number
}

export interface ComplimentMetrics {
  aspect: string
  count: number
  averageRating: number
  examples: string[]
}

export interface FeedbackDashboard {
  metrics: SatisfactionMetrics
  recentFeedbacks: CustomerFeedback[]
  pendingActions: FeedbackAction[]
  activeSurveys: NPSSurvey[]
  alerts: FeedbackAlert[]
  recommendations: DashboardRecommendation[]
}

export interface FeedbackAction {
  id: string
  feedbackId: string
  type: 'response_required' | 'escalation' | 'follow_up' | 'survey_trigger'
  title: string
  description: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  assignedTo: string
  dueDate: string
  createdAt: string
}

export interface FeedbackAlert {
  id: string
  type: 'low_nps' | 'high_complaints' | 'response_overdue' | 'trend_decline'
  title: string
  message: string
  severity: 'info' | 'warning' | 'error'
  data: Record<string, any>
  createdAt: string
}

export interface DashboardRecommendation {
  id: string
  type: 'process_improvement' | 'customer_outreach' | 'product_enhancement' | 'training'
  title: string
  description: string
  impact: 'low' | 'medium' | 'high'
  effort: 'low' | 'medium' | 'high'
  priority: number
  actions: string[]
}

// Survey Templates
export interface SurveyTemplate {
  id: string
  name: string
  description: string
  category: string
  type: FeedbackType
  questions: SurveyQuestion[]
  defaultSettings: SurveySettings
  isPublic: boolean
  usageCount: number
  rating: number
  createdBy: string
  createdAt: string
  updatedAt: string
}

// Customer Journey Feedback
export interface JourneyFeedback {
  id: string
  customerId: string
  journeyStage: 'onboarding' | 'project_start' | 'development' | 'delivery' | 'support' | 'renewal'
  touchpoint: string
  experience: 'positive' | 'neutral' | 'negative'
  rating: number
  feedback: string
  improvements: string[]
  timestamp: string
  context: Record<string, any>
}

// Feedback Analytics
export interface FeedbackAnalytics {
  id: string
  period: string
  startDate: string
  endDate: string
  totalFeedbacks: number
  averageRating: number
  npsScore: number
  sentimentScore: number
  categoryDistribution: Record<string, number>
  sourceDistribution: Record<string, number>
  resolutionMetrics: ResolutionMetrics
  customerSegmentAnalysis: SegmentAnalysis[]
  wordCloud: WordCloudData[]
  correlations: CorrelationData[]
}

export interface ResolutionMetrics {
  totalResolved: number
  averageResolutionTime: number
  firstResponseTime: number
  escalationRate: number
  customerSatisfactionAfterResolution: number
}

export interface SegmentAnalysis {
  segment: string
  criteria: string
  feedbackCount: number
  averageRating: number
  npsScore: number
  topIssues: string[]
  satisfactionTrend: number
}

export interface WordCloudData {
  word: string
  frequency: number
  sentiment: 'positive' | 'neutral' | 'negative'
  category: string
}

export interface CorrelationData {
  factor1: string
  factor2: string
  correlation: number
  significance: number
  insight: string
}

// Automated Actions
export interface AutomatedAction {
  id: string
  name: string
  description: string
  trigger: ActionTrigger
  conditions: ActionCondition[]
  actions: Action[]
  isActive: boolean
  executionCount: number
  lastExecuted?: string
  createdBy: string
  createdAt: string
  updatedAt: string
}

export interface ActionTrigger {
  type: 'feedback_received' | 'nps_score' | 'rating_threshold' | 'keyword_detected'
  parameters: Record<string, any>
}

export interface ActionCondition {
  field: string
  operator: string
  value: any
  logicalOperator?: 'AND' | 'OR'
}

export interface Action {
  type: 'send_email' | 'create_ticket' | 'assign_to_user' | 'trigger_survey' | 'update_status'
  parameters: Record<string, any>
  delay?: number
}

// Customer Satisfaction Profile
export interface CustomerSatisfactionProfile {
  customerId: string
  customerName: string
  overallSatisfaction: number
  npsScore: number
  lastFeedbackDate: string
  feedbackCount: number
  averageRating: number
  satisfactionTrend: 'improving' | 'stable' | 'declining'
  riskLevel: 'low' | 'medium' | 'high'
  keyStrengths: string[]
  keyIssues: string[]
  recommendations: string[]
  journeyFeedbacks: JourneyFeedback[]
  recentInteractions: CustomerInteraction[]
}

export interface CustomerInteraction {
  id: string
  type: 'feedback' | 'survey' | 'support' | 'meeting' | 'call'
  date: string
  summary: string
  outcome: string
  satisfaction?: number
  followUpRequired: boolean
}
