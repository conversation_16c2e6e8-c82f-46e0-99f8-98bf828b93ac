export enum ReviewStatus {
  PENDING = 'pending',
  IN_REVIEW = 'in_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  REVISION_REQUESTED = 'revision_requested',
  EXPIRED = 'expired',
}

export enum AnnotationType {
  COMMENT = 'comment',
  SUGGESTION = 'suggestion',
  ISSUE = 'issue',
  APPROVAL = 'approval',
  QUESTION = 'question',
}

export enum VisualizationMode {
  OVERVIEW = 'overview',
  DETAILED = 'detailed',
  INTERACTIVE_3D = 'interactive_3d',
  ARCHITECTURE = 'architecture',
  WORKFLOW = 'workflow',
}

export interface SolutionReview {
  id: string
  solutionId: string
  solutionName: string
  solutionVersion: string
  customerId: string
  customerName: string
  status: ReviewStatus
  submittedAt: string
  reviewDeadline?: string
  completedAt?: string
  currentStep: number
  totalSteps: number
  annotations: ReviewAnnotation[]
  approvals: ReviewApproval[]
  revisions: ReviewRevision[]
  attachments: ReviewAttachment[]
  metadata: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface ReviewAnnotation {
  id: string
  type: AnnotationType
  title: string
  content: string
  position: AnnotationPosition
  authorId: string
  authorName: string
  authorAvatar?: string
  targetElement?: string
  targetComponent?: string
  priority: 'low' | 'medium' | 'high'
  status: 'open' | 'resolved' | 'dismissed'
  responses: AnnotationResponse[]
  attachments: string[]
  createdAt: string
  updatedAt: string
  resolvedAt?: string
  resolvedBy?: string
}

export interface AnnotationPosition {
  x: number
  y: number
  z?: number
  viewMode: VisualizationMode
  viewport?: {
    width: number
    height: number
    zoom: number
    offset: { x: number; y: number }
  }
}

export interface AnnotationResponse {
  id: string
  content: string
  authorId: string
  authorName: string
  authorAvatar?: string
  createdAt: string
}

export interface ReviewApproval {
  id: string
  stepId: string
  stepName: string
  approverId: string
  approverName: string
  approverRole: string
  status: 'pending' | 'approved' | 'rejected'
  comments?: string
  conditions?: string[]
  timestamp: string
  deadline?: string
}

export interface ReviewRevision {
  id: string
  version: string
  description: string
  changes: RevisionChange[]
  requestedBy: string
  requestedAt: string
  implementedBy?: string
  implementedAt?: string
  status: 'requested' | 'in_progress' | 'completed' | 'rejected'
}

export interface RevisionChange {
  id: string
  type: 'component_change' | 'feature_addition' | 'feature_removal' | 'configuration_update'
  description: string
  componentId?: string
  oldValue?: any
  newValue?: any
  reason: string
  impact: 'low' | 'medium' | 'high'
  estimatedCost?: number
  estimatedTime?: number
}

export interface ReviewAttachment {
  id: string
  name: string
  type: 'image' | 'document' | 'video' | 'model_3d' | 'other'
  url: string
  size: number
  uploadedBy: string
  uploadedAt: string
  description?: string
  tags: string[]
}

export interface SolutionVisualization {
  id: string
  solutionId: string
  mode: VisualizationMode
  config: VisualizationConfig
  assets: VisualizationAsset[]
  interactions: VisualizationInteraction[]
  createdAt: string
  updatedAt: string
}

export interface VisualizationConfig {
  camera: {
    position: { x: number; y: number; z: number }
    target: { x: number; y: number; z: number }
    fov: number
  }
  lighting: {
    ambient: number
    directional: { intensity: number; position: { x: number; y: number; z: number } }
  }
  materials: Record<string, MaterialConfig>
  animations: AnimationConfig[]
  ui: {
    showGrid: boolean
    showAxes: boolean
    showLabels: boolean
    theme: 'light' | 'dark'
  }
}

export interface MaterialConfig {
  color: string
  opacity: number
  metalness?: number
  roughness?: number
  emissive?: string
  texture?: string
}

export interface AnimationConfig {
  id: string
  name: string
  duration: number
  loop: boolean
  autoPlay: boolean
  keyframes: AnimationKeyframe[]
}

export interface AnimationKeyframe {
  time: number
  properties: Record<string, any>
  easing?: string
}

export interface VisualizationAsset {
  id: string
  type: '3d_model' | 'texture' | 'animation' | 'sound'
  url: string
  name: string
  metadata: Record<string, any>
}

export interface VisualizationInteraction {
  id: string
  type: 'click' | 'hover' | 'drag' | 'zoom' | 'rotate'
  targetId: string
  action: InteractionAction
  conditions?: InteractionCondition[]
}

export interface InteractionAction {
  type: 'highlight' | 'show_info' | 'navigate' | 'animate' | 'toggle_visibility'
  parameters: Record<string, any>
}

export interface InteractionCondition {
  property: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than'
  value: any
}

export interface VersionComparison {
  id: string
  solutionId: string
  baseVersion: string
  compareVersion: string
  differences: VersionDifference[]
  summary: ComparisonSummary
  createdAt: string
}

export interface VersionDifference {
  id: string
  type: 'added' | 'removed' | 'modified' | 'moved'
  category: 'component' | 'configuration' | 'cost' | 'timeline' | 'feature'
  path: string
  description: string
  oldValue?: any
  newValue?: any
  impact: 'low' | 'medium' | 'high'
  costImpact?: number
  timeImpact?: number
}

export interface ComparisonSummary {
  totalChanges: number
  addedItems: number
  removedItems: number
  modifiedItems: number
  costDifference: number
  timeDifference: number
  impactLevel: 'low' | 'medium' | 'high'
  recommendations: string[]
}

export interface ReviewWorkflow {
  id: string
  name: string
  description: string
  steps: ReviewWorkflowStep[]
  isActive: boolean
  createdBy: string
  createdAt: string
  updatedAt: string
}

export interface ReviewWorkflowStep {
  id: string
  name: string
  description: string
  order: number
  type: 'review' | 'approval' | 'revision' | 'notification'
  assigneeRole: string
  requiredApprovals: number
  timeLimit?: number
  conditions?: WorkflowCondition[]
  actions: WorkflowAction[]
}

export interface WorkflowCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than'
  value: any
}

export interface WorkflowAction {
  type: 'send_notification' | 'update_status' | 'assign_user' | 'create_task'
  parameters: Record<string, any>
}

export interface ReviewMetrics {
  totalReviews: number
  pendingReviews: number
  approvedReviews: number
  rejectedReviews: number
  averageReviewTime: number
  customerSatisfaction: number
  revisionRate: number
  onTimeCompletionRate: number
  annotationsPerReview: number
  topIssueCategories: { category: string; count: number }[]
}

export interface ReviewNotification {
  id: string
  reviewId: string
  type: 'review_assigned' | 'deadline_approaching' | 'review_completed' | 'revision_requested'
  recipientId: string
  title: string
  message: string
  isRead: boolean
  createdAt: string
}

// Customer-specific interfaces
export interface CustomerReviewDashboard {
  activeReviews: SolutionReview[]
  completedReviews: SolutionReview[]
  pendingActions: ReviewAction[]
  notifications: ReviewNotification[]
  metrics: CustomerReviewMetrics
}

export interface CustomerReviewMetrics {
  totalReviews: number
  averageReviewTime: number
  satisfactionScore: number
  revisionsRequested: number
  onTimeCompletions: number
}

export interface ReviewAction {
  id: string
  type: 'review_required' | 'approval_needed' | 'revision_response' | 'deadline_approaching'
  reviewId: string
  title: string
  description: string
  priority: 'low' | 'medium' | 'high'
  dueDate?: string
  url: string
}

// 3D Viewer specific types
export interface Scene3D {
  id: string
  name: string
  objects: Object3D[]
  camera: Camera3D
  lighting: Lighting3D
  environment: Environment3D
}

export interface Object3D {
  id: string
  name: string
  type: 'mesh' | 'group' | 'light' | 'camera'
  position: { x: number; y: number; z: number }
  rotation: { x: number; y: number; z: number }
  scale: { x: number; y: number; z: number }
  material?: string
  geometry?: string
  children?: Object3D[]
  userData?: Record<string, any>
}

export interface Camera3D {
  type: 'perspective' | 'orthographic'
  position: { x: number; y: number; z: number }
  target: { x: number; y: number; z: number }
  fov?: number
  near: number
  far: number
}

export interface Lighting3D {
  ambient: { color: string; intensity: number }
  directional: Array<{
    color: string
    intensity: number
    position: { x: number; y: number; z: number }
    castShadow: boolean
  }>
  point: Array<{
    color: string
    intensity: number
    position: { x: number; y: number; z: number }
    distance: number
  }>
}

export interface Environment3D {
  background: string | { type: 'color' | 'gradient' | 'skybox'; value: any }
  fog?: { type: 'linear' | 'exponential'; color: string; near: number; far: number }
  ground?: { color: string; size: number; divisions: number }
}
