export enum DeliveryStatus {
  PREPARING = 'preparing',
  READY_FOR_DELIVERY = 'ready_for_delivery',
  IN_DELIVERY = 'in_delivery',
  DELIVERED = 'delivered',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  REVISION_REQUIRED = 'revision_required',
}

export enum AcceptanceStatus {
  PENDING = 'pending',
  IN_REVIEW = 'in_review',
  TESTING = 'testing',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CONDITIONAL_APPROVAL = 'conditional_approval',
}

export enum DeliverableType {
  SOFTWARE = 'software',
  DOCUMENT = 'document',
  DESIGN = 'design',
  TRAINING = 'training',
  HARDWARE = 'hardware',
  SERVICE = 'service',
  OTHER = 'other',
}

export enum QualityLevel {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  SATISFACTORY = 'satisfactory',
  NEEDS_IMPROVEMENT = 'needs_improvement',
  POOR = 'poor',
}

export interface DeliveryPackage {
  id: string
  projectId: string
  projectName: string
  name: string
  description: string
  version: string
  status: DeliveryStatus
  deliverables: Deliverable[]
  checklist: DeliveryChecklistItem[]
  qualityReport: QualityReport
  acceptanceTests: AcceptanceTest[]
  deliveryDate: string
  acceptanceDeadline: string
  acceptedDate?: string
  rejectedDate?: string
  clientId: string
  clientName: string
  deliveredBy: string
  acceptedBy?: string
  notes: string
  attachments: DeliveryAttachment[]
  revisionHistory: DeliveryRevision[]
  metadata: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface Deliverable {
  id: string
  packageId: string
  name: string
  description: string
  type: DeliverableType
  status: AcceptanceStatus
  version: string
  files: DeliverableFile[]
  requirements: string[]
  acceptanceCriteria: string[]
  testResults: TestResult[]
  qualityScore: number
  estimatedSize: number
  actualSize: number
  deliveryDate: string
  acceptanceDate?: string
  assigneeId: string
  assigneeName: string
  reviewerId?: string
  reviewerName?: string
  comments: DeliverableComment[]
  tags: string[]
  priority: 'low' | 'medium' | 'high' | 'critical'
  createdAt: string
  updatedAt: string
}

export interface DeliverableFile {
  id: string
  deliverableId: string
  name: string
  originalName: string
  path: string
  url: string
  size: number
  type: string
  mimeType: string
  checksum: string
  version: string
  isMainFile: boolean
  previewUrl?: string
  thumbnailUrl?: string
  downloadCount: number
  uploadedBy: string
  uploadedAt: string
  lastModified: string
  metadata: Record<string, any>
}

export interface DeliverableComment {
  id: string
  deliverableId: string
  content: string
  type: 'comment' | 'issue' | 'approval' | 'rejection'
  authorId: string
  authorName: string
  authorRole: string
  attachments: string[]
  isResolved: boolean
  resolvedBy?: string
  resolvedAt?: string
  createdAt: string
  updatedAt: string
}

export interface DeliveryChecklistItem {
  id: string
  packageId: string
  category: string
  title: string
  description: string
  isRequired: boolean
  isCompleted: boolean
  completedBy?: string
  completedAt?: string
  evidence?: string[]
  notes?: string
  order: number
}

export interface QualityReport {
  id: string
  packageId: string
  overallScore: number
  overallLevel: QualityLevel
  criteria: QualityCriteria[]
  issues: QualityIssue[]
  recommendations: string[]
  reviewedBy: string
  reviewedAt: string
  approvedBy?: string
  approvedAt?: string
  summary: string
  nextReviewDate?: string
}

export interface QualityCriteria {
  id: string
  name: string
  description: string
  weight: number
  score: number
  maxScore: number
  level: QualityLevel
  evidence: string[]
  comments: string
  reviewedBy: string
  reviewedAt: string
}

export interface QualityIssue {
  id: string
  title: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  category: string
  status: 'open' | 'in_progress' | 'resolved' | 'closed'
  assigneeId?: string
  assigneeName?: string
  dueDate?: string
  resolution?: string
  resolvedBy?: string
  resolvedAt?: string
  attachments: string[]
  createdBy: string
  createdAt: string
  updatedAt: string
}

export interface AcceptanceTest {
  id: string
  packageId: string
  name: string
  description: string
  type: 'functional' | 'performance' | 'security' | 'usability' | 'integration'
  status: 'pending' | 'running' | 'passed' | 'failed' | 'skipped'
  priority: 'low' | 'medium' | 'high' | 'critical'
  steps: TestStep[]
  expectedResult: string
  actualResult?: string
  evidence: string[]
  executedBy?: string
  executedAt?: string
  duration?: number
  environment: string
  prerequisites: string[]
  notes?: string
  createdBy: string
  createdAt: string
  updatedAt: string
}

export interface TestStep {
  id: string
  testId: string
  order: number
  action: string
  expectedResult: string
  actualResult?: string
  status: 'pending' | 'passed' | 'failed' | 'skipped'
  screenshot?: string
  notes?: string
}

export interface TestResult {
  id: string
  deliverableId: string
  testId: string
  testName: string
  status: 'passed' | 'failed' | 'skipped'
  score?: number
  maxScore?: number
  executionTime: number
  errorMessage?: string
  evidence: string[]
  executedBy: string
  executedAt: string
}

export interface DeliveryAttachment {
  id: string
  packageId: string
  name: string
  description: string
  type: 'document' | 'image' | 'video' | 'archive' | 'other'
  url: string
  size: number
  uploadedBy: string
  uploadedAt: string
  isPublic: boolean
  downloadCount: number
}

export interface DeliveryRevision {
  id: string
  packageId: string
  version: string
  description: string
  changes: RevisionChange[]
  requestedBy: string
  requestedAt: string
  implementedBy?: string
  implementedAt?: string
  status: 'requested' | 'in_progress' | 'completed' | 'rejected'
  reason: string
  impact: 'low' | 'medium' | 'high'
  estimatedEffort: number
  actualEffort?: number
}

export interface RevisionChange {
  id: string
  revisionId: string
  type: 'addition' | 'modification' | 'removal' | 'fix'
  component: string
  description: string
  before?: string
  after?: string
  effort: number
  priority: 'low' | 'medium' | 'high' | 'critical'
}

export interface AcceptanceWorkflow {
  id: string
  packageId: string
  name: string
  description: string
  steps: AcceptanceStep[]
  currentStepId: string
  status: AcceptanceStatus
  startedAt: string
  completedAt?: string
  assignedTo: string[]
  notifications: WorkflowNotification[]
  escalations: WorkflowEscalation[]
}

export interface AcceptanceStep {
  id: string
  workflowId: string
  name: string
  description: string
  type: 'review' | 'test' | 'approval' | 'notification'
  order: number
  status: 'pending' | 'in_progress' | 'completed' | 'skipped' | 'failed'
  assigneeId: string
  assigneeName: string
  dueDate: string
  completedAt?: string
  requirements: string[]
  deliverables: string[]
  actions: StepAction[]
  conditions: StepCondition[]
}

export interface StepAction {
  id: string
  stepId: string
  type: 'approve' | 'reject' | 'request_changes' | 'escalate' | 'notify'
  name: string
  description: string
  parameters: Record<string, any>
  isAvailable: boolean
}

export interface StepCondition {
  id: string
  stepId: string
  type: 'all_tests_passed' | 'quality_score_above' | 'no_critical_issues' | 'custom'
  description: string
  parameters: Record<string, any>
  isMet: boolean
}

export interface WorkflowNotification {
  id: string
  workflowId: string
  type: 'step_assigned' | 'step_completed' | 'deadline_approaching' | 'escalation'
  recipientId: string
  recipientName: string
  title: string
  message: string
  sentAt: string
  readAt?: string
}

export interface WorkflowEscalation {
  id: string
  workflowId: string
  stepId: string
  reason: string
  escalatedTo: string
  escalatedBy: string
  escalatedAt: string
  resolvedAt?: string
  resolution?: string
}

export interface DeliveryMetrics {
  totalPackages: number
  deliveredPackages: number
  acceptedPackages: number
  rejectedPackages: number
  averageDeliveryTime: number
  averageAcceptanceTime: number
  onTimeDeliveryRate: number
  firstTimeAcceptanceRate: number
  averageQualityScore: number
  customerSatisfactionScore: number
  revisionRate: number
  criticalIssuesCount: number
}

export interface DeliveryDashboard {
  metrics: DeliveryMetrics
  recentDeliveries: DeliveryPackage[]
  pendingAcceptances: DeliveryPackage[]
  qualityTrends: QualityTrend[]
  upcomingDeadlines: DeliveryDeadline[]
  topIssues: QualityIssue[]
  teamPerformance: TeamPerformance[]
}

export interface QualityTrend {
  date: string
  averageScore: number
  packageCount: number
  issueCount: number
}

export interface DeliveryDeadline {
  packageId: string
  packageName: string
  type: 'delivery' | 'acceptance'
  deadline: string
  daysRemaining: number
  status: DeliveryStatus | AcceptanceStatus
  assignee: string
}

export interface TeamPerformance {
  userId: string
  userName: string
  deliveredPackages: number
  averageQualityScore: number
  onTimeDeliveryRate: number
  revisionRate: number
  customerRating: number
}

// File preview and management types
export interface FilePreview {
  id: string
  fileId: string
  type: 'image' | 'pdf' | 'video' | 'audio' | 'text' | 'code' | 'office' | 'archive'
  previewUrl: string
  thumbnailUrl?: string
  metadata: FileMetadata
  isPreviewable: boolean
  requiresDownload: boolean
}

export interface FileMetadata {
  dimensions?: { width: number; height: number }
  duration?: number
  pageCount?: number
  wordCount?: number
  lineCount?: number
  language?: string
  encoding?: string
  compression?: string
  author?: string
  title?: string
  subject?: string
  keywords?: string[]
  createdDate?: string
  modifiedDate?: string
}

export interface FileVersion {
  id: string
  fileId: string
  version: string
  size: number
  checksum: string
  uploadedBy: string
  uploadedAt: string
  changes: string
  isActive: boolean
}

export interface FileShare {
  id: string
  fileId: string
  sharedWith: string[]
  permissions: FilePermission[]
  expiresAt?: string
  accessCount: number
  lastAccessedAt?: string
  createdBy: string
  createdAt: string
}

export interface FilePermission {
  userId: string
  userName: string
  role: string
  permissions: ('view' | 'download' | 'comment' | 'edit')[]
  grantedAt: string
  grantedBy: string
}
